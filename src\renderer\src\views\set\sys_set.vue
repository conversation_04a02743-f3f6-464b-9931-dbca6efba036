<template>
	<div class="sys_base">
		<!-- 系统导航 -->
		<!-- <div :class="showHideWord?'sys_left':'sys_left sys_left_b'">
			<Tabar :softName='chatName' @changeChat='changeChat' @showHideWordFun='showHideWordFun'></Tabar>
		</div> -->
		<!-- 主要内容区 -->
		<div :class="tabarStore.showHideWord ? 'main_cont contact_main_cont' : 'main_cont contact_main_cont small_tab'">
			<!-- 全局设置 -->
			<div class="contact_cont">
				<div class="tit">
					<div class="tit_info">
						<h2>全局设置</h2>
						<span>Global Settings</span>
					</div>
				</div>
				<div class="sys_set">
					<div class="items">
						<h3>系统设置</h3>
					</div>
					<div class="items">
						<h3>窗口关闭</h3>
						<div class="setList">
							<div :class="configSet.win ? 'item on' : 'item'" @click="changeSysSet('win')">
								<p><img v-if="configSet.win" :src="icon44" alt=""></p>
								<span class="cursor-pointer">窗口关闭时最小化到托盘</span>
							</div>
						</div>
					</div>
					<div class="items">
						<h3>Ai客服</h3>
						<div class="setList">
							<div :class="configSet.ai ? 'item on' : 'item'" @click="changeSysSet('ai')">
								<p><img v-if="configSet.ai" :src="icon44" alt=""></p>
								<span class="cursor-pointer">{{ configSet.ai ? '开启ai客服' : '关闭ai客服' }}</span>
							</div>
						</div>
					</div>
					<div v-show="false" class="ml-2">
						<div class="flex justify-start items-center text-black mb-5">
							<label
								class="before:content-['*'] before:text-red relative before:absolute before:top-0 before:-left-1 before:-translate-[60%,0%]">
								客服在线时间:
							</label>
							<div class="ml-5">
								<el-time-picker @change="changeSysSet('aiTimeRang')" value-format="HH:mm:ss"
									v-model="configSet.aiTimeRang" is-range range-separator="至" start-placeholder="开始时间"
									end-placeholder="结束时间" />
							</div>
						</div>
						<div class="text-gray-400 text-sm">
							<p class="mb-5px">提示</p>
							<ol type="1" class="pl-1em">
								<li>开启Ai客服后，需将蓝海译通软件保持在线状态，电脑不要息屏;</li>
								<li>人工介入后，AI客服将自动退出，请注意聊天对话;</li>
							</ol>
						</div>

					</div>
					<div class="items" v-show="platform.isMacOS">
						<h3>消息展示</h3>
						<div class="setList">
							<div :class="configSet.message ? 'item on' : 'item'" @click="changeSysSet('message')">
								<p><img v-if="configSet.message" :src="icon44" alt=""></p>
								<span class="cursor-pointer">显示未读信息</span>
							</div>
						</div>
					</div>
					<!-- <div class="items">
						<h3>是否暗黑模式</h3>
						<div class="setList">
							<div  :class="configSet.is_dark?'item on':'item'" @click="changeSysSet('is_dark')">
								<p><img v-if="configSet.is_dark" :src="icon44" alt=""></p>
								<span>是否暗黑模式显示</span>
							</div>
						</div>
					</div> -->
				</div>
				<!-- 系统版本 -->
				<div class="version">
					<div class="version_num">当前版本：{{ app_version }}</div>
					<!-- <p @click="checkUpdate">检查更新</p> -->
				</div>
			</div>
		</div>
	</div>

</template>

<script setup>
import Tabar from '../../components/tabar.vue'
import {
	ref, watch,
	onMounted,
	onUnmounted,
	toRaw
} from 'vue'
// import {
// 	getWorkOrderSql
// } from "../../api/account.js"
import { useRouter, useRoute } from 'vue-router'
import { useTabStore } from "@renderer/store/index.js"
import { getAiIsSet } from '../../api/account.js' // 导入 getAiIsSet 接口函数
import { ElMessage } from 'element-plus'
const tabarStore = useTabStore()// 导入 useRouter
const router = useRouter()  // 获取路由实例
const route = useRoute();  // 获取当前路由对象

const platform = ref(window.platform)
// 导入本地图片
const icon44 = new URL('../../assets/img/icon44.png', import.meta.url).href;
// 全局设置
const configSet = ref({
	'win': false,
	'message': false,
	ai: false,
	aiTimeRang: []
})
// 系统当前版本
const app_version = ref(localStorage.getItem('version'))
// 传递左侧栏目隐藏或展示文字方法
const showHideWord = ref(true)
watch(
	() => route.query,
	(newParams, oldParams) => {
		// 设置左侧是否显示
		if (newParams.chat == 'slide') {
			showHideWord.value = newParams.type == 'true' ? true : false
		}
	},
	{ immediate: true } // 立即执行一次，以确保初始参数也被捕获
);
// 改变平台
const changeChat = (cN) => {
	chatName.value = cN
}

// es-loash
import { debounce } from 'lodash'
const changeSysSet = debounce(async (type) => {
	if (type == 'win') {
		configSet.value.win = !configSet.value.win
		// 全局设置
		window.electron.ipcRenderer.send('sysSet', configSet.value.win, 'win')
	} else if (type == 'message') {
		configSet.value.message = !configSet.value.message
		window.electron.ipcRenderer.send('sysSet', configSet.value.message, "message")
	} else if (type == 'is_dark') {
		configSet.value.is_dark = !configSet.value.is_dark
		document.documentElement.setAttribute('data-theme', configSet.value.is_dark ? 'dark' : 'light')
		// 全局设置
		window.electron.ipcRenderer.send('sysSet', configSet.value.is_dark, 'dark')
	} else if (type === 'ai') {
		if (!configSet.value.ai === true) {
			const r = await getAiIsSet({
				dataset: localStorage.getItem("uuid")
			})
			if (!r) {
				ElMessage.error("请设置Ai回复数据集......")
				return
			}
		}
		configSet.value.ai = !configSet.value.ai;
		tabarStore.isOpenAi = configSet.value.ai
		window.electron.ipcRenderer.send('sysSet', configSet.value.ai, "ai")
	} else if (type === 'aiTimeRang') {
		window.electron.ipcRenderer.send('sysSet', toRaw(configSet.value.aiTimeRang), "aiTimeRang")
	}

	localStorage.setItem('configSet', JSON.stringify(configSet.value))
}, 500)

onMounted(() => {
	if (localStorage.getItem('configSet')) {
		configSet.value = JSON.parse(localStorage.getItem('configSet'))
		window.electron.ipcRenderer.send('sysSet', configSet.value.win, 'win')
		window.electron.ipcRenderer.send('sysSet', configSet.value.message, 'message')
		document.documentElement.setAttribute('data-theme', configSet.value.is_dark ? 'dark' : 'light')
	}
})

const checkUpdate = () => {
	window.electron.ipcRenderer.send('check-for-update')
}
</script>

<style lang="scss">
@import url("../../assets/style/contact.scss");
</style>