.sys_base{
	.sys_left_b{
		width: 85px;
	}
	.small_tab{
		margin-left:102px!important;
		 width: calc(100% - 112px)!important;
		 .webCont{
		 	width: calc(100vw - 110px)!important;
		 }
	}
}
.index_main_cont{
	display: flex;
	height: 100%;
	// width: calc(100% - 122px)!important;
	.add_btn{
		color:#000;
		line-height: 40px;
		background-color: #f00;
	}
	.soft_set{
		width: 100px;
	}
	.index_content{
		width: calc(100% - 10px);
		border-radius: 10px;
		overflow: hidden;
		.top{
			display: flex;
			align-items: center;
			height: 40%;
			.left{
				// width: calc(100% - 488px);
				width: calc(71.2% - 20px);
				margin-right: 20px;
				height: 100%;
				border-radius: 10px;
				background-size: 100% 100%;
				.wel_info{
					height: 70%;
					border-radius: 10px;
					padding:20px;
					box-shadow: 0px 8px 16px 0px rgba(143, 149, 178, 0.15);
					background:url(../img/index_bg.png) no-repeat center center;
					background-size: cover;
					.index_wel{
						display: flex;
						align-items: center;
						width:100%;
						height: 60%;
						justify-content: space-between;
						.logo{
							width: 70%;
							display: flex;
							align-items: center;
							img{
								width: 96px;
								height: 96px;
							}
							.wel_tit{
								padding-left: 20px;
								color:#fff;
								width: calc(100% - 100px);
								h2{
									font-family: Inter;
									font-size: 32px;
									font-weight: 900;
									line-height: 38.73px;
									text-align: left;
									white-space: nowrap;
									overflow: hidden;
									width: 100%;
									text-underline-position: from-font;
									text-decoration-skip-ink: none;
									
								}
								p{
									font-family: Nunito Sans;
									font-size: 16px;
									font-weight: 400;
									padding-top:10px;
									line-height: 21.82px;
									text-align: left;
									white-space: nowrap;
									overflow: hidden;
									text-underline-position: from-font;
									text-decoration-skip-ink: none;

								}
							}
						}
						.income{
							font-family: Nunito Sans;
							font-size: 14px;
							font-weight: 700;
							line-height: 32px;
							text-align: left;
							text-underline-position: from-font;
							text-decoration-skip-ink: none;
							color:rgba(85, 119, 255, 1);
							background-color: #fff;
							width: 148px;
							height: 44px;
							line-height: 44px;
							text-align: center;
							border-radius: 40px;
						}

						.income2{
							    font-family: Nunito Sans;
    font-size: 14px;
    font-weight: 700;
    line-height: 32px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: rgb(85, 119, 255);
    background-color: #fff;
    width: 72px;
		margin-left: 10px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    border-radius: 40px;
						}

					}
					.account_info{
						display: flex;
						align-items: center;
						width: 100%;
						background-color: #fff;
						border-radius: 10px;
						height: 40%;
						padding-left:20px;
						color:rgba(8, 23, 53, 1);
						.item{
							position: relative;
							width: 16.6%;
							margin:0 5px;
							padding:0 10px;
							// overflow: hidden;
							p{
								font-family: Nunito Sans;
								font-size: 12px;
								font-weight: 700;
								line-height: 16.37px;
								text-align: left;
								padding-bottom: 6px;
								white-space: nowrap;
								width: 100%;
								overflow: hidden;
								text-overflow: ellipsis;
								text-underline-position: from-font;
								text-decoration-skip-ink: none;
							}
							span{
								font-size: 16px;
								font-weight: 800;
								line-height: 21.82px;
								text-align: left;
								width: 100%;
								display: block;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
								color:rgba(85, 119, 255, 1)
							}
							.flagshow{
								position: absolute;
								bottom:-20px;
								background-color: rgba(255,255,255,0.6);
								box-shadow: 0 0 3px #ccc;
								padding:0 30px;
								z-index: 99;
								white-space: nowrap;
								color:rgba(85, 119, 255, 1);
								border-radius: 20px;
								display: none;
							}
						}
						.item:hover{
							.flagshow{
								display: block;
							}
						}
						.item::after{
							content: '';
							position: absolute;
							height:50%;
							border-right:1px solid rgba(204, 204, 204, 1);
							right:0;
							top:25%;
						}
						.item:last-child::after{
							content: '';
							border:0;
						}
						
					}
				}
				.tech_info{
					height: calc(30% - 10px);
					margin-top:10px;
					border-radius: 10px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					.item{
						background-color: rgba(255, 235, 246, 1);
						width: 24%;
						height: 100%;
						border-radius: 10px;
						display: flex;
						overflow: hidden;
						align-items: center;
						box-shadow: 0px 8px 16px 0px rgba(143, 149, 178, 0.15);
						img{
							width: 64px;
							height: 64px;
							margin:0 3%;
						}
						.title{
							color:rgba(8, 23, 53, 1);
							width: calc(100% - 64px - 6%);
							p{
								font-family: Nunito Sans;
								font-size:19px;
								font-weight: 700;
								line-height: 24px;
								text-align: left;
								padding-bottom: 6px;
								display: block;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
								text-underline-position: from-font;
								text-decoration-skip-ink: none;
							}
							span{
								//styleName: Paragraph 200;
								font-family: Nunito Sans;
								font-size: 14px;
								font-weight: 400;
								line-height: 20px;
								text-align: left;
								display: block;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
								text-underline-position: from-font;
								text-decoration-skip-ink: none;
								color: #8F95B2;
;
							}
						}
					}
					.item:nth-child(2){
						background-color: rgba(231, 250, 255, 1);
					}
					.item:nth-child(3){
						background-color: rgba(255, 243, 220, 1);
					}
					.item:nth-child(4){
						background-color: #DEFFF2;
					}
				}
			}
			.right{
				background:var(--bg-color-index-task);
				box-shadow: 0px 8px 16px 0px rgba(143, 149, 178, 0.15);
				// width: 468px;
				width: 28.8%;
				height: 100%;
				overflow: hidden;
				border-radius: 10px;
				.right_cont{
					background:url(../img/icon19.png) no-repeat bottom right;
					background-position-y: calc(100% + 40px);
					width: 100%;
					height: 100%;
					padding:20px;
					overflow: hidden;
					color:var(--font-color-login);
					.tast_tit{
						display: flex;
						justify-content: space-between;
						width: 100%;
						.tit{
							width: 120px;
							h2{
								font-family: Nunito Sans;
								font-size: 24px;
								font-weight: 700;
								line-height: 32px;
								text-align: left;
								display: block;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
								text-underline-position: from-font;
								text-decoration-skip-ink: none;
							}
							span{
								font-family: Nunito Sans;
								font-size: 16px;
								font-weight: 400;
								line-height: 21.33px;
								text-align: left;
								display: block;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
								text-underline-position: from-font;
								text-decoration-skip-ink: none;
								color:var(--font-color-index-task-time);
							}
						}
						.time{
							font-family: Nunito Sans;
							font-size: 16px;
							font-weight: 400;
							line-height: 20px;
							text-align: left;
							
							display: block;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
							text-underline-position: from-font;
							text-decoration-skip-ink: none;
							color:var(--font-color-index-task-time);
						}
					}
					.tast_list{
						padding-top:30px;
						h3{
							font-family: Nunito Sans;
							font-size: 20px;
							font-weight: 700;
							line-height: 32px;
							text-align: left;
							text-underline-position: from-font;
							text-decoration-skip-ink: none;
							background: linear-gradient(-45deg, #FF8C8C 0%, #F4A9E4 37%, #AB94F1 67.5%, #6B7EF8 100%);
							background: -webkit-linear-gradient(-45deg, #FF8C8C 0%, #F4A9E4 37%, #AB94F1 67.5%, #6B7EF8 100%); /* Chrome, Safari */
							-webkit-background-clip: text; /* Chrome, Safari */
							background-clip: text;
							-webkit-text-fill-color: transparent; /* Chrome, Safari */
							color: transparent; /* 兼容不支持background-clip的浏览器 */
							display: inline-block;
							margin: 0 auto;
							padding-bottom: 20px;
						}
						.tast_icon{
							display: flex;
							.item{
								padding-left: 20px;
								padding-bottom: 10px;
								border-bottom:1px solid #ccc;
								img{
									width: 30px;
									height: 30px;
								}
							}
							.item:first-child{
								padding-left:0;
							}
						}
					}
					.remark{
						padding-top: 10px;
						font-family: Nunito Sans;
						font-size: 14px;
						font-weight: 400;
						line-height: 20px;
						text-align: left;
						text-underline-position: from-font;
						text-decoration-skip-ink: none;
						color:var(--font-color-index-remark);
					}
				}
			}
		}
		.bottom{
			display: flex;
			height: calc(60% - 20px);
			margin-top:20px;
			.left{
				background-color: var(--bg-color-cont);
				// width: calc(100% - 620px);
				overflow: hidden;
				width: 60%;
				height: 100%;
				margin-right: 20px;
				border-radius: 10px;
				padding:20px;
				.left_top_tit{
					display: flex;
					justify-content: space-between;
					padding-bottom: 30px;
					.tit{
						white-space: nowrap;
						h2{
							font-family: Nunito Sans;
							font-size: 24px;
							font-weight: 700;
							line-height: 32px;
							text-align: left;
							text-underline-position: from-font;
							text-decoration-skip-ink: none;
							color:var(--font-color-login);
						}
						span{
							font-family: Nunito Sans;
							font-size: 16px;
							font-weight: 400;
							line-height: 21.33px;
							text-align: left;
							text-underline-position: from-font;
							text-decoration-skip-ink: none;
							color:var(--font-color-index-task-time);
						}
					}
					.manyDay{
						display: flex;
						align-items: center;
						color: var(--font-color-login);
						text-align: center;
						.item{
							margin:0 20px;
							p{
								font-family: Nunito Sans;
								font-size: 16px;
								font-weight: 400;
								line-height: 20px;
								text-align: left;
								text-underline-position: from-font;
								text-decoration-skip-ink: none;
								color:#8F95B2;
								padding-bottom: 10px;
								white-space: nowrap;
							}
							span{
								font-family: Nunito Sans;
								font-size: 24px;
								font-weight: 700;
								line-height: 24px;
								text-align: left;
								text-underline-position: from-font;
								text-decoration-skip-ink: none;
							}
						}
					}
					.selPlant{
						display: flex;
						align-items: center;
						background-color: var(--bg-color-index-echars);
						padding:0 15px;
						height:47px;
						border-radius: 40px;
						.img{
							width: 24px;
							height: 24px;
							img{
								width: 24px;
								height: 24px;	
							}
						}
						.select_div{
							width: 100px;
							.el-select__wrapper{
								height: 45px;
								border: 1px solid var(--bg-color-index-echars);
								outline: 1px solid var(--bg-color-index-echars);
								background: var(--bg-color-index-echars);
								box-shadow:none;
								font-size: 16px;
								.el-select__placeholder{
									color:var(--font-color-index-echars)
								}
							}
							.el-select__wrapper.is-focused{
								box-shadow: none;
							}

						}
					}
				}
				.left_TT{
					width: 100%;
					height: calc(100% - 90px);
					.leftT{
						width: 100%;
						height: 100%;
						border-radius: 10px;
					}
				}
			}
			.right{
				// width: 620px;
				width: 40%;
				height: 100%;
				padding:20px;
				overflow-y: auto;
				background-color: var(--bg-color-cont);
				border-radius: 10px;
				.plant_tit{
					display: flex;
					justify-content: space-between;
					h2{
						font-family: Nunito Sans;
						font-size: 24px;
						font-weight: 700;
						line-height: 32px;
						text-align: left;
						text-underline-position: from-font;
						text-decoration-skip-ink: none;
						color:var(--font-color-login);
					}
					span{
						font-family: Nunito Sans;
						font-size: 16px;
						font-weight: 400;
						line-height: 21.33px;
						text-align: left;
						text-underline-position: from-font;
						text-decoration-skip-ink: none;
						color:var(--font-color-index-task-time);
					}
					.switch{
						font-family: Inter;
						font-size: 18px;
						font-weight: 400;
						letter-spacing: 0.035em;
						text-align: left;
						text-underline-position: from-font;
						text-decoration-skip-ink: none;
						color:var(--font-color-index-remark)
					}
				}
				.plant_list{
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					padding-top: 30px;
					.item{
						width: 50%;
						min-width: 220px;
						display: flex;
						align-items: center;
						padding-bottom: 30px;
						
						.name{
							color:var(--font-color-login);
							width: 170px;
							display: flex;
							align-items: center;
							img{
								width:40px;
								height: 40px;
							}
							span{
								font-family: Inter;
								font-size: 18px;
								font-weight: 400;
								padding-left:12px;
								letter-spacing: 0.035em;
								text-align: left;
								text-underline-position: from-font;
								text-decoration-skip-ink: none;

							}
						}
					}
				}
			}
		}
	}
	
}
body {
	border: 1px solid #00000054;
}