import { Platform } from '.'

export class TelegramHandler extends Platform {
  constructor(platform) {
    super(platform)
  }
  // #region 属性 ******************
  // WS用户信息
  userId
  userName
  // 已经新增过的粉丝信息，【，】分隔
  newFansRecord = ''
  // 当前正在聊天的用户信息
  chatUserId = ''
  isMac = navigator.userAgentData?.platform === 'macOS' || /Mac/.test(navigator.userAgent)
  scrollTimer = null
  scrolling = false
  // #endregion

  // #region 监听事件 ******************
  _o(mutations, observer) {
    this.getUserInfo()
    this.getNewFans()
    if (!this.scrolling) {
      this.translateList()
    }
  }
  _u(location) {
    this.getChatUserId()
  }
  sendMessageToInput(options) {
    this.sendMessageEvent({
      text: options.message,
      is_send: options.type === 'send',
      input: this.sI.input,
      btn: this.sI.sendBtn
    })
  }
  // #endregion

  // #region 获取必要信息 ******************
  // 获取主账号信息
  getUserInfo() {
    let userAuth = localStorage.getItem('user_auth')
    if (userAuth && !this.userId) {
      userAuth = JSON.parse(userAuth)
      this.userId = userAuth.id
      let index = -1
      const databaseNameArr = [
        'tweb',
        'tweb-account-1',
        'tweb-account-2',
        'tweb-account-3',
        'tweb-account-4'
      ]

      const storeName = 'users'

      const func = () => {
        if (index < databaseNameArr.length - 1) index++
        else return

        const databaseName = databaseNameArr[index]
        const request = indexedDB.open(databaseName)

        request.onsuccess = (event) => {
          const db = event.target.result

          try {
            // ✅ 判断 store 是否存在
            if (!db.objectStoreNames.contains(storeName)) {
              console.warn(`数据库 ${databaseName} 不含 store: ${storeName}，跳过`)
              db.close()
              func()
              return
            }

            const transaction = db.transaction([storeName], 'readonly')
            const objectStore = transaction.objectStore(storeName)
            const messageLog = objectStore.get(String(this.userId))

            messageLog.onsuccess = () => {
              if (messageLog.result) {
                const param = {
                  userId: this.userId,
                  platform: this.platform,
                  username: messageLog.result.username,
                  nickName: messageLog.result.sortName,
                  phone: messageLog.result.phone,
                  session_id: this.viewSessionId
                }
                console.log(`✅ 在 ${databaseName} 中找到用户信息`, param)
                this.sendPlatformUserInfo(param)
              } else {
                console.log(`❌ ${databaseName} 中未找到用户`)
                func()
              }
              db.close()
            }

            messageLog.onerror = (event) => {
              console.error('读取用户失败:', event.target.error)
              db.close()
              func()
            }
          } catch (error) {
            console.error('处理过程中出错:', error)
            db.close()
            func()
          }
        }

        request.onerror = (event) => {
          console.error(`❌ 无法打开数据库 ${databaseName}:`, event.target.error)
          func()
        }
      }

      func()
    }

  }
  // 获取当前聊天的用户信息
  getChatUserId() {
    const activeChat = document.querySelector(this.sI.activeChat)
    if (activeChat) {
      let activeFanId = activeChat.getAttribute('data-peer-id')
      let nickName = activeChat.querySelector('.peer-title').innerText
      if (activeFanId && this.chatUserId != activeFanId) {
        this.chatUserId = activeFanId
        console.log('当前正在聊天的用户id切换为：', this.chatUserId)
        this.bindInputKeydown()
        this.setMaskBtn('mask-btn', this.sI.maskDependEl)
        this.translateList()
        this.setScrollEvent()
        let params = {
          mainAccount: this.userId,
          fansId: this.chatUserId,
          nickName: nickName,
          platform: this.platform
        }
        console.log('当前聊天人信息', params)
        this.sendCurrentSessionFansInfo(params)
      }
    } else {
      this.chatUserId = ''
    }
  }
  // 通过未读消息获取新粉丝
  getNewFans() {
    if (!document.querySelector('.unread')) return
    const chats = document.querySelectorAll(this.sI.chatList)
    let unReadCount = 0
    if (chats) {
      const unreadListInfo = []
      for (let chat of chats) {
        let user_id = chat.getAttribute('data-peer-id')
        const unread = chat.querySelector('.unread')
        // 如果是服务号不算入新粉丝
        if (unread && unread.querySelector('.verified-icon')) {
          continue
        }
        let no_read_user = unread ? unread.innerText : 0
        if (no_read_user > 0 && user_id > 0) {
          unReadCount += 1
          let fansUserInfo = {
            id: user_id,
            nickName: chat.querySelector(this.sI.newFansNickName).innerText
          }
          if (this.newFansRecord.indexOf(user_id) === -1) {
            this.newFansRecord += `,${user_id}`
            unreadListInfo.push(fansUserInfo)
          }
        }
      }
      console.log('sendUnreadCount', unReadCount)
      this.sendUnReadCount(unReadCount)

      if (unreadListInfo.length > 0) {
        this.sendNewFansList({
          viewSessionId: this.viewSessionId,
          platform: this.platform,
          mainAccount: this.userId,
          unreadListInfo
        })
      }
    }
  }
  // #endregion

  // #region 主输入框操作 ******************
  // 绑定输入框回车事件，在发送前翻译
  bindInputKeydown() {
    const func = () => {
      const inputEL = document.querySelector(this.sI.input)
      console.log('bindInputKeydown=====', inputEL)
      if (inputEL) {
        inputEL.addEventListener(
          'keydown',
          async (event) => {
            this.setMaskBtn('mask-btn', this.sI.maskDependEl)
            if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
              event.preventDefault()
              event.stopPropagation()
              event.stopImmediatePropagation()
              this.sendMessage()
            }
          },
          true
        )
      } else {
        setTimeout(() => {
          window.requestAnimationFrame(func)
        }, 500)
      }
    }
    window.requestAnimationFrame(func)
  }
  // 取到当前输入框数据，翻译后发送
  async sendMessage() {
    // 获取输入框内容
    let contentDom = document.querySelector(this.sI.input)
    let v = contentDom.innerText
    // this.setEditable(this.sI.input, false)
    const config = this.translater?.config
    const isTranslate = config && config.trans_over
    if (v.trim() && isTranslate) {
      this.clearInput(this.sI.input)
      let tV = await this.translater.translateInput(v)
      this.sendMessageEvent({
        text: tV || v,
        is_send: true,
        input: this.sI.input,
        btn: this.sI.sendBtn
      })
    } else {
      document.querySelector(this.sI.sendBtn).click()
      // this.setEditable(this.sI.input, true)
    }
  }
  // #endregion

  // #region 响应操作 ******************
  // 翻译列表中的历史消息
  async translateList() {
    let listEL = document.querySelector(this.sI.messageListElSelector)
    if (listEL) {
      const reciveMessageElList = listEL.querySelectorAll(this.sI.reciveMessageElSelector)
      const sendMessageElList = listEL.querySelectorAll(this.sI.sendMessageElSelector)
      const length = reciveMessageElList.length + sendMessageElList.length
      if (length == 0) return

      if (document.querySelectorAll('[data-translated]').length >= length) {

      }
      const callback = () => {
        if (document.querySelectorAll('[data-translated]').length >= length) {

          this.scrollToBottom()
        }
      }
      reciveMessageElList.forEach((el) => {
        const msgItem = this.setOutMsgDom(el)
        if (!msgItem) return
        this.translater.translateMessage(msgItem, { callback: callback, type: 'in' })
      })

      sendMessageElList.forEach((el) => {
        this.translater.translateMessage(el, { callback: callback, type: 'out' })
      })
    }
  }
  // 聊天在翻译后滚动到底部
  scrollToBottom() {
    let scrollDomScroll = document.querySelector(this.sI.scrollContent)
    if (scrollDomScroll) {
      // 计算元素的最大滚动高度
      var maxScrollTop = scrollDomScroll.scrollHeight - scrollDomScroll.clientHeight
      // 将元素的scrollTop设置为其最大滚动高度，以滚动到底部
      if (maxScrollTop - scrollDomScroll.scrollTop < 950) {
        scrollDomScroll.scrollTop = maxScrollTop
      }
    } else {
      // 如果元素不存在，您可能想要在这里处理错误或执行其他操作
      console.error('未找到可滚动的元素')
    }
  }
  // 监听滚动事件
  setScrollEvent() {
    const scrollDom = document.querySelector(this.sI.scrollContent)
    if (scrollDom) {
      scrollDom.addEventListener('scroll', () => {
        this.scrolling = true
        if (this.scrollTimer) clearTimeout(this.scrollTimer)
        this.scrollTimer = setTimeout(() => {
          this.scrolling = false
          this.translateList()
        }, 500)
      })
    } else {
      setTimeout(() => {
        this.setScrollEvent()
      }, 500)
    }
  }
  // 清空whatsapp 输入框方法
  clearInput(_element) {
    let element = document.querySelector(_element)
    if (!element) return
    element.innerHTML = ''
  }
  // // 禁用方法-- 禁止用户输入数据
  setEditable(query, editStatus) {
    let element = document.querySelector(query)
    if (editStatus) {
      element.removeAttribute('contenteditable')
      element.setAttribute('contenteditable', 'true')
    } else {
      element.removeAttribute('contenteditable')
      element.setAttribute('contenteditable', 'false')
    }
  }
  // 配置按钮蒙层，在原有发送前进行处理
  setMaskBtn(maskId, maskParent) {
    if (!document.querySelector('#' + maskId)) {
      const maskBtn = document.createElement('div')
      maskBtn.id = maskId // 设置 id
      const maskBtnParent = document.querySelector(maskParent)
      if (maskBtnParent) {
        maskBtnParent.style.position = 'relative'
        maskBtn.style.position = 'absolute'
        maskBtn.style.top = '0'
        maskBtn.style.left = '0'
        maskBtn.style.width = '100%'
        maskBtn.style.height = '100%'
        maskBtn.style.zIndex = '999'
        maskBtnParent.appendChild(maskBtn)

        maskBtn.addEventListener('click', async (e) => {
          e.stopPropagation()
          this.sendMessage()
        })
      }
    } else {
      setTimeout(() => {
        this.setMaskBtn(maskId, maskParent)
      }, 500)
    }
  }
  /**
   * @function 模拟发送消息事件
   * @param {Object} options text: 发送的消息内容, is_send: 是否发送, input: 输入框选择器, btn: 发送按钮选择器
   */
  sendMessageEvent(options) {
    let element = document.querySelector(options.input)
    element.focus()
    // 模拟输入事件
    element.innerText = options.text
    if (options.is_send) {
      setTimeout(() => {
        document.querySelector(options.btn)?.click()
        // this.setEditable(this.sI.input, true)
        setTimeout(() => {
          this.scrollToBottom()
        }, 1000)
      }, 50)
    }
  }
  // 设置发送消息的DOM，让他包裹在一个span中
  setOutMsgDom(element) {
    let msgItem = element.querySelector('.blueglob-msg')
    if (msgItem) return msgItem
    // 获取第一个子节点并判断是否为文字节点
    const firstChild = element.firstChild
    if (firstChild.nodeType === Node.TEXT_NODE) {
      // 如果是文字节点，获取它的文本内容
      const textContent = firstChild.textContent.trim()
      // 将该节点替换成span包裹的文本
      const span = document.createElement('span')
      span.className = 'blueglob-msg'
      span.textContent = textContent
      element.replaceChild(span, firstChild)
      let msgItem = element.querySelector('.blueglob-msg')
      return msgItem
    } else {
      return firstChild
    }
  }
  // #endregion
}