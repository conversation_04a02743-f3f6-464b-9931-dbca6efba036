// import https  from 'https';
import http from 'http'
import CryptoJS from 'crypto-js'
import url from 'url'
import axios from 'axios'

axios
  .get(
    `https://translate.google.com/translate_a/single?client=at&sl=auto&tl=en&dt=t&q=hello 啊 树先生`
  )
  .then((res) => {
    let data = res.data
    let tranlateText = ''
    data[0].forEach((item) => {
      tranlateText += item[0]
    })
    return tranlateText
  })

// 正式环境
const baseUrl = import.meta.env.VITE_BASE_API
// 测试环境
// const baseUrl = "https://lhytapi.blueglob.com"
const urlStr = baseUrl + '/api/translate/translate'
const urlObj = url.parse(urlStr)
export async function getTranslate(data, token) {
  const postData = JSON.stringify(data)
  const currentTime = new Date() // 创建一个Date对象，表示当前时间
  const timestamp = Math.floor(currentTime.getTime() / 1000)
  return axios
    .post(urlStr, data, {
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        token: token,
        timestamp: timestamp,
        sign: sha256(postData + timestamp + token)
      },
      timeout: 10000
    })
    .then((res) => {
      return res.data
    })
    .catch(async (error) => {
      if (error.code === 'ECONNABORTED' && error.config.url === '/api/translate/translate') {
        let config = error.config
        try {
          let res = await axios.get(
            `https://translate.google.com/translate_a/single?client=at&sl=${config.fromLang}&tl=${config.toLang}&dt=t&q=${config.text}`
          )
          let data = res.data
          let tranlateText = ''
          data[0].forEach((item) => {
            tranlateText += item[0]
          })
          return {
            code: 1,
            data: {
              result: tranlateText
            }
          }
        } catch (_err) {
          return error
        }
      }

      return error
    })

  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        token: token,
        timestamp: timestamp,
        sign: sha256(postData + timestamp + token)
      }
    }

    const req = http.request(options, (res) => {
      let data = ''

      res.setEncoding('utf8')
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => {
        try {
          const message = JSON.parse(data)
          resolve(message)
        } catch (error) {
          reject(error)
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.write(postData)
    req.end()
  })
}

const urlStrSave = baseUrl + '/api/chat/addChatRecord'
const urlObjSave = url.parse(urlStrSave)
export async function saveChat(data, token) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data)
    const currentTime = new Date() // 创建一个Date对象，表示当前时间
    const timestamp = Math.floor(currentTime.getTime() / 1000)
    const options = {
      hostname: urlObjSave.hostname,
      port: urlObjSave.port || 80,
      path: urlObjSave.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        token: token,
        timestamp: timestamp,
        sign: sha256(postData + timestamp + token)
      }
    }
    const req = http.request(options, (res) => {
      let data = ''
      res.setEncoding('utf8')
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => {
        try {
          const message = JSON.parse(data)
          resolve(message)
        } catch (error) {
          reject(error)
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.write(postData)
    req.end()
  })
}

// 获取元素选择器
const urlSelectorInfo = baseUrl + '/api/index/platformCssConfig'
const urlSelectorUrl = url.parse(urlSelectorInfo)
export async function getSelectorInfoFunc(data, token) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data)
    const currentTime = new Date() // 创建一个Date对象，表示当前时间
    const timestamp = Math.floor(currentTime.getTime() / 1000)
    const options = {
      hostname: urlSelectorUrl.hostname,
      port: urlSelectorUrl.port || 80,
      path: urlSelectorUrl.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        token: token,
        timestamp: timestamp,
        sign: sha256(postData + timestamp + token)
      }
    }
    const req = http.request(options, (res) => {
      let data = ''
      res.setEncoding('utf8')
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => {
        try {
          const message = JSON.parse(data)
          resolve(message)
        } catch (error) {
          reject(error)
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.write(postData)
    req.end()
  })
}

// 自动翻译aichat
const autoReplyAiUrlInfo = baseUrl + '/api/index/platformCssConfig'
const autoReplyAiUrl = new url.URL(autoReplyAiUrlInfo)
export async function AutoReplyAiChat(data, token) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data)
    const currentTime = new Date() // 创建一个Date对象，表示当前时间
    const timestamp = Math.floor(currentTime.getTime() / 1000)
    const options = {
      hostname: autoReplyAiUrl.hostname,
      port: autoReplyAiUrl.port || 80,
      path: autoReplyAiUrl.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        token: token,
        timestamp: timestamp,
        sign: sha256(postData + timestamp + token)
      }
    }
    const req = http.request(options, (res) => {
      let data = ''
      res.setEncoding('utf8')
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => {
        try {
          const message = JSON.parse(data)
          resolve(message)
        } catch (error) {
          reject(error)
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.write(postData)
    req.end()
  })
}

function sha256(message) {
  return CryptoJS.SHA256(message).toString()
}
