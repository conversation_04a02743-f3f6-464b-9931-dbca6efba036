<template>
	<div class="sys_base">
		<!-- 系统导航 -->
		<!-- <div :class="showHideWord?'sys_left':'sys_left sys_left_b'">
			<Tabar :softName='chatName' @changeChat='changeChat' @showHideWordFun='showHideWordFun'></Tabar>
		</div> -->
		<!-- 主要内容区 -->
		<div :class="tabarStore.showHideWord ? 'main_cont script_main_cont' : 'main_cont script_main_cont small_tab'">
			<!-- 推荐话术 -->
			<div class="script_cont">
				<div class="tit">
					<div class="tit_info">
						<h2>推荐话术</h2>
						<span>Recommended words</span>
					</div>
				</div>
				<!-- 推荐话术 -->
				<div class="scriptList">
					<el-table :data="tableData" tooltip-effect="dark" height="100%" empty-text="无数据"
						style="width: 100%;text-align: center;">
						<el-table-column prop="reply_name" label="标题" width="150">
						</el-table-column>
						<el-table-column prop="type_name" label="标签" width="150">
						</el-table-column>
						<el-table-column prop="content" label="内容" min-width="120">
						</el-table-column>
					</el-table>
				</div>

			</div>
		</div>
	</div>

</template>

<script setup>
import Tabar from '../../components/tabar.vue'
import {
	ref, watch,
	onMounted,
	onUnmounted
} from 'vue'
import {
	quickListSql
} from "../../api/account.js"
import { useRouter, useRoute } from 'vue-router'  // 导入 useRouter
import { useTabStore } from "@renderer/store/index.js"
const tabarStore = useTabStore()
const router = useRouter()  // 获取路由实例
const route = useRoute();  // 获取当前路由对象
// 导入本地图片
const icon55 = new URL('../../assets/img/icon55.svg', import.meta.url).href;
const icon56 = new URL('../../assets/img/icon56.svg', import.meta.url).href;
const icon57 = new URL('../../assets/img/icon57.svg', import.meta.url).href;
// 表格数据
const tableData = ref([])
const quickList = () => {
	quickListSql({}).then(response => {
		if (response.code == 1) {
			tableData.value = response.data
		}
	})
}
quickList()
// 支持平台列表
const chatName = ref()

// 改变平台
const changeChat = (cN) => {
	chatName.value = cN
}
// 设置是否显示设置信息页面
const is_open = ref(false)
const openSetPage = (son_b) => {
	is_open.value = son_b
}
// 传递左侧栏目隐藏或展示文字方法
const showHideWord = ref(true)
watch(
	() => route.query,
	(newParams, oldParams) => {
		// 设置左侧是否显示
		if (newParams.chat == 'slide') {
			showHideWord.value = newParams.type == 'true' ? true : false
		}
	},
	{ immediate: true } // 立即执行一次，以确保初始参数也被捕获
);
</script>

<style lang="scss">
@import url("../../assets/style/script.scss");
</style>