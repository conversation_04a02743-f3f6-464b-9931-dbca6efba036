import { Platform } from '.'
export class FacebookHandler extends Platform {
  constructor(platform) {
    super(platform)
  }
  idRegular = /(?<=\/t\/)[\d]{1,}(?=\/?)|$/
  userId = undefined
  userName = undefined
  prefilePicture = undefined
  chatUserId = undefined
  isFull = false

  async init(translater) {
    await super.init(translater)
    this.getUserId()
    this.bindInputFunction()
    this.smallBindInputFunction()
  }
  _o(mutations, observer) {
    // 绑定 input
    this.bindInputFunction()
    this.smallBindInputFunction()
    // 聊天页面
    this.translateList()
    // 获取未读消息的会话
    this.getUnreadSessionUserToFans()
    // 获取主账号信息
    this.getUserInfo()
    //
    this.autoReplyHandler()
    // 小窗
    this.getSmallChatUserInfo()
  }

  _u(location) {
    this.isFull = this.isFullScreenChat()
    this.getCurrentSessionUserId()
  }

  getCurrentSessionUserId() {
    if (this.isFull && this.idRegular.test(location.pathname)) {
      const func = () => {
        let id = this.idRegular.exec(location.pathname)[0]
        if (id == this.userId) {
          return
        }
        this.chatUserId = id
        let nickName = ''
        let sessionListEl = document.querySelector(this.sI.sessionListElSelector)
        if (sessionListEl) {
          let flist = sessionListEl.querySelectorAll(this.sI.sessionUserA)
          flist?.forEach((a) => {
            let m = this.idRegular.exec(a.href)
            if (m[0] === id) {
              nickName = a.querySelector(this.sI.sessionUserNickNameElSelector)?.innerText
            }
          })

          let params = {
            mainAccount: this.userId,
            fansId: id,
            nickName: nickName ? nickName : id,
            platform: this.platform
          }
          this.sendCurrentSessionFansInfo(params)
        } else {
          setTimeout(() => {
            window.requestAnimationFrame(func)
          }, 500)
        }
      }

      window.requestAnimationFrame(func)
    }
  }

  getUnreadSessionUserToFans() {
    if (this.isFull && this.idRegular.test(location.pathname)) {
      const listEl = document.querySelector(this.sI.sessionListElSelector)
      const unreadELList = listEl?.querySelectorAll(this.sI.unreadElSelector)
      if (unreadELList) {
        console.log('sendUnreadCount')
        this.sendUnReadCount(unreadELList.length)
        if (unreadELList.length === 0) {
          return
        }
      }
      const unreadListInfo = []
      unreadELList?.forEach((el) => {
        if (el.hasAttribute('aira-isread')) {
          return
        } else {
          el.setAttribute('aira-isread', 'true')
        }
        let a = el.closest('a')
        let nickName = a.querySelector(this.sI.sessionUserNickNameElSelector)?.innerText
        let matchList = this.idRegular.exec(a.href)
        if (matchList) {
          unreadListInfo.push({
            id: matchList[0],
            nickName
          })
        }
      })

      if (unreadListInfo.length > 0) {
        this.sendNewFansList({
          viewSessionId: this.viewSessionId,
          platform: this.platform,
          mainAccount: this.userId,
          unreadListInfo
        })
      }
    }
  }

  autoReplyHandler() {

    if (this.isFull && this.idRegular.test(location.pathname)) {
      this.aiReply(async () => {
        const listEl = document.querySelector(this.sI.sessionListElSelector)
        const unreadELList = listEl?.querySelectorAll(this.sI.unreadElSelector)
        let flist = listEl.querySelectorAll(this.sI.sessionUserA)
        if (unreadELList) {
          for (const element of unreadELList) {
            await (async () => {
              element.closest('a')?.click()
              const rect = element?.getBoundingClientRect();
              this.clickAt({
                x: Math.round(rect.left + rect.width / 2),
                y: Math.round(rect.top + rect.height / 2)
              })
              return new Promise((resolve, reject) => {
                setTimeout(async () => {
                  let messages = Array.from(
                    document.querySelectorAll("[data-originaltext]")
                  )
                  let question = ''
                  let m = messages.slice(-5).map((item) => {
                    let tlist = item.innerText.split('\n')
                    let content = tlist[0]
                    let role = ""
                    if (item.closest(this.sI.reciveMessageElSelector)) {
                      role = "user"
                      question = content
                    } else {
                      role = "assistant"
                    }

                    return {
                      content,
                      role
                    }
                  })
                  console.log('m:', m)
                  let value = await this.getAiReply({ question, messageList: m, chat_id: this.userId })
                  if (value) {
                    await this.inputMessage(value.content, true)
                  }
                  let e = flist[flist.length - 1];
                  e?.click()
                  const rect2 = e?.getBoundingClientRect();
                  this.clickAt({
                    x: Math.round(rect2.left + rect2.width / 2),
                    y: Math.round(rect2.top + rect2.height / 2)
                  })
                  resolve()
                }, 2000)
              })
            })()
          }


        }
      })
    }
  }

  clearInput(selector) {
    let keyDownA = {}

    if (navigator.userAgentData?.platform === 'macOS' || /Mac/.test(navigator.userAgent)) {
      keyDownA = {
        key: 'a',
        metaKey: true,
        bubbles: true,
        cancelable: true
      }
    } else {
      keyDownA = {
        key: 'a',
        ctrlKey: true,
        bubbles: true,
        cancelable: true
      }
    }
    const selectAllEvent = new KeyboardEvent('keydown', keyDownA)
    // 创建模拟删除操作的事件（这里还是以Backspace为例，也可以尝试Delete键等）
    const deleteEvent = new KeyboardEvent('keydown', {
      key: 'Backspace',
      bubbles: true,
      cancelable: true
    })
    let element
    if (this.isFull) {
      element = document.querySelector(selector)
    } else {
      // 如果存在active的小窗聊天
      const activeSamll = document.querySelector(this.sI.activeSmall)
      if (activeSamll) {
        element = activeSamll.querySelector(selector)
      }
    }
    // 先触发全选操作
    element.dispatchEvent(selectAllEvent)
    element.dispatchEvent(deleteEvent)
  }

  bindInputFunction() {
    if (this.isFull && this.idRegular.test(location.pathname)) {
      // console.log('message page')
      const func = (timestamp) => {
        const inputEL = document.querySelector(this.sI.inputElSelector)
        if (inputEL) {
          inputEL.addEventListener(
            'keydown',
            async (event) => {
              inputEL.setAttribute('aria-bind', 'true')
              if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
                event.stopPropagation()
                event.stopImmediatePropagation()
                this.changeInputEditStatus(false, this.sI.inputElSelector)
                const v = event.target.innerText
                this.clearInput(this.sI.inputElSelector)
                if (v.trim()) {
                  let tV = await this.translater.translateInput(v)
                  tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, true)
                } else {
                  await this.inputMessage(v, true)
                }
                this.changeInputEditStatus(true, this.sI.inputElSelector)
                this.switchShadowState(false)
              }
            },
            {
              capture: true
            },
            true
          )

          inputEL.addEventListener('input', (event) => {
            event.stopPropagation()
            event.stopImmediatePropagation()
            this.createMaskDiv()
          })
        } else {
          setTimeout(() => {
            window.requestAnimationFrame(func)
          }, 500)
        }
      }
      window.requestAnimationFrame(func)
    }
  }

  sendMessageToInput({ type, message }) {
    this.inputMessage(message, type === 'send')
  }

  inputMessage(value, is_send = false) {
    console.log('value,', value)

    return new Promise((resolve) => {
      const activeSamll = document.querySelector(this.sI.activeSmall)
      let element
      if (this.isFull) {
        element = document.querySelector(this.sI.inputElSelector)
      } else {
        if (activeSamll) element = activeSamll.querySelector(this.sI.smallInputElSelector)
      }
      element.focus()
      const inputEvent = new InputEvent('input', {
        bubbles: true,
        cancelable: true,
        data: value,
        inputType: 'insertText'
      })
      element.dispatchEvent(inputEvent)
      element.keydown = null
      element.onkeydown = null
      if (is_send) {
        setTimeout(() => {
          if (this.isFull) {
            document.querySelector(this.sI.sendButtonElSelector).click()
          } else {
            // 如果存在active的小窗聊天
            if (activeSamll) {
              activeSamll.querySelector(this.sI.smallSendButtonElSelector)?.click()
            }
          }
          resolve()
        }, 50)
      }
    })
  }

  switchShadowState(flag) {
    const activeSamll = document.querySelector(this.sI.activeSmall)
    let m
    if (this.isFull) {
      m = document.querySelector('.myShadow')
    } else {
      m = activeSamll.querySelector('.myShadow')
    }

    if (!m) {
      return
    }

    if (flag) {
      m.style.display = 'block'
    } else {
      m.style.display = 'none'
    }
  }

  createMaskDiv() {
    const activeSamll = document.querySelector(this.sI.activeSmall)
    const bol = this.isFull
      ? !document.querySelector('.myShadow')
      : !activeSamll?.querySelector('.myShadow')
    if (bol) {
      // 创建遮罩---禁止点击按钮
      let shadwoDiv = document.createElement('div')
      shadwoDiv.className = 'myShadow'
      shadwoDiv.style.position = 'absolute'
      shadwoDiv.style.width = '100%'
      shadwoDiv.style.height = '100%'
      shadwoDiv.style.top = '0px'
      shadwoDiv.style.right = '0px'
      shadwoDiv.style.zIndex = 9999

      let inputElSelector
      let maskContainer
      if (this.isFull) {
        inputElSelector = this.sI.inputElSelector
        maskContainer = document.querySelector(this.sI.sendButtonElSelector)
      } else {
        inputElSelector = this.sI.smallInputElSelector
        if (activeSamll)
          maskContainer = activeSamll.querySelector(this.sI.smallSendButtonElSelector)
      }
      if (maskContainer) {
        maskContainer.style.position = 'relative'
        maskContainer.appendChild(shadwoDiv)
      }

      shadwoDiv.addEventListener('click', async (event) => {
        event.stopPropagation()
        this.changeInputEditStatus(false, inputElSelector)
        let config = this.translater.config
        const isTranslate = config && config.trans_over
        const inputEl = this.isFull
          ? document.querySelector(inputElSelector)
          : activeSamll?.querySelector(inputElSelector)
        const v = inputEl.innerText
        console.log(v)
        this.clearInput(inputElSelector)
        if (isTranslate && v.trim()) {
          let tV = await this.translater.translateInput(v)
          tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, true)
        } else {
          await this.inputMessage(v, true)
        }
        this.changeInputEditStatus(true, inputElSelector)
        this.switchShadowState(false)
      })
    } else {
      this.switchShadowState(true)
    }
  }

  changeInputEditStatus(editStatus, selector) {
    try {
      let element
      if (this.isFull) {
        element = document.querySelector(selector)
      } else {
        // 如果存在active的小窗聊天
        const activeSamll = document.querySelector(this.sI.activeSmall)
        if (activeSamll) {
          element = activeSamll.querySelector(selector)
        }
      }
      if (!element) return
      if (editStatus) {
        element.removeAttribute('contenteditable')
        element.setAttribute('contenteditable', 'true')
      } else {
        element.removeAttribute('contenteditable')
        element.setAttribute('contenteditable', 'false')
      }
    } catch (error) {
      console.error(error)
    }
  }

  async getUserId() {
    let u = await cookieStore.get('c_user')
    if (u) {
      this.userId = u.value
      let params = {
        userId: this.userId,
        platform: this.platform,
        phone: this.userId,
        nickName: this.userName || this.userId,
        session_id: this.viewSessionId
      }
      this.sendPlatformUserInfo(params)
    }
  }

  async getUserInfo() {
    if (window.location.pathname === '/') {
      const userEl = document.querySelector(this.sI.userElSelector)
      let isChange = false
      if (userEl) {
        if (this.userName !== userEl.innerText) {
          isChange = true
          this.userName = userEl.innerText
        }
        const prefilePictureEl = userEl.querySelector('image')
        const href = prefilePictureEl ? prefilePictureEl.href.baseVal : undefined
        this.prefilePicture = href ? href : undefined
      }
      if (isChange) {
        let params = {
          userId: this.userId,
          platform: this.platform,
          phone: this.userId,
          nickName: this.userName || this.userId,
          session_id: this.viewSessionId
        }
        this.sendPlatformUserInfo(params)
      }
    }
  }

  async translateList() {
    const func = (_parent, chatId) => {
      let listEL = _parent.querySelector(this.sI.messageListElSelector)
      if (listEL) {
        const reciveMessageElList = listEL.querySelectorAll(this.sI.reciveMessageElSelector)
        reciveMessageElList.forEach((el, index) => {
          this.translater.translateMessage(el, { fansId: chatId, type: 'in' })
        })

        const sendMessageElList = listEL.querySelectorAll(this.sI.sendMessageElSelector)
        sendMessageElList.forEach((el, index) => {
          this.translater.translateMessage(el, { fansId: chatId, type: 'out' })
        })
      }
    }
    if (this.isFull) {
      func(document)
    } else {
      // 只翻译当前active的小窗
      const activeSamll = document.querySelector(this.sI.activeSmall)
      if (activeSamll) {
        let id = activeSamll.querySelector(this.sI.activeSmallId)?.getAttribute('href')
        if (id) {
          id = id.replace(/\//g, '')
          func(activeSamll, id)
        }

      }
    }
  }

  // 小窗聊天操作
  // 获取小窗当前聊天人信息
  getSmallChatUserInfo() {
    // 如果存在active的小窗聊天
    const activeSamll = document.querySelector(this.sI.activeSmall)
    if (activeSamll) {
      // 找到他的聊天id赋值给chatUserId

      let id = activeSamll.querySelector(this.sI.activeSmallId)?.getAttribute('href')
      let nickName = activeSamll.querySelector(this.sI.activeSmallNickName)?.innerText
      if (id) {
        id = id.replace(/\//g, '')
        if (this.chatUserId != id) {
          this.chatUserId = id

          let params = {
            mainAccount: this.userId,
            fansId: id,
            nickName: nickName ? nickName : id,
            platform: this.platform
          }
          console.log('小窗聊天更新聊天人信息', params)
          this.sendCurrentSessionFansInfo(params)
        }
      }
    }
  }
  smallBindInputFunction() {
    // 如果存在active的小窗聊天
    const activeSamll = document.querySelector(this.sI.activeSmall)
    if (activeSamll) {
      const func = (timestamp) => {
        const inputEL = activeSamll.querySelector(this.sI.smallInputElSelector)
        if (inputEL) {
          inputEL.addEventListener(
            'keydown',
            async (event) => {
              inputEL.setAttribute('aria-bind', 'true')
              if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
                event.stopPropagation()
                event.stopImmediatePropagation()
                this.changeInputEditStatus(false, this.sI.smallInputElSelector)
                let config = this.translater.config
                const isTranslate = config && config.trans_over
                const v = event.target.innerText
                this.clearInput(this.sI.smallInputElSelector)
                if (isTranslate && v.trim()) {
                  let tV = await this.translater.translateInput(v)
                  tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, true)
                }
                this.changeInputEditStatus(true, this.sI.smallInputElSelector)
                this.switchShadowState(false)
              }
            },
            {
              capture: true
            },
            true
          )
          inputEL.addEventListener('input', (event) => {
            event.stopPropagation()
            event.stopImmediatePropagation()
            this.createMaskDiv()
          })
        } else {
          setTimeout(() => {
            window.requestAnimationFrame(func)
          }, 500)
        }
      }
      window.requestAnimationFrame(func)
    }
  }

  isFullScreenChat() {
    return (
      location.pathname.includes('/messages/t/') || location.pathname.includes('/messages/e2ee/t/')
    )
  }
}
