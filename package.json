{"name": "lanhaiyitong", "version": "2.0.13", "description": "蓝海译通", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "start": "electron-vite preview", "dev": "electron-vite dev --mode development", "build": "electron-vite build --mode production", "build:test": "electron-vite build --mode test", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:win-test": "npm run build:test && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:mac-test": "npm run build:test && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "axios": "^1.7.9", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "electron-updater": "^6.1.7", "idb": "^8.0.3"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@lottiefiles/dotlottie-vue": "^0.8.0", "@rushstack/eslint-patch": "^1.10.3", "@unocss/preset-wind4": "^66.1.3", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "echarts": "^5.6.0", "electron": "^31.0.2", "electron-builder": "^24.13.3", "electron-builder-squirrel-windows": "^25.1.8", "electron-reload": "^2.0.0-alpha.1", "electron-vite": "^2.3.0", "element-plus": "^2.9.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.26.0", "lowdb": "^7.0.1", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "prettier": "^3.3.2", "sass": "^1.83.1", "sass-loader": "^16.0.4", "unocss": "^66.1.3", "uuid": "^11.0.5", "vite": "^5.3.1", "vue": "^3.4.30", "vue-draggable-resizable": "^3.0.0", "vue-hooks-plus": "^2.4.0", "vue-i18n": "next", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "build": {"appId": "lanhai2025.v1", "productName": "蓝海译通", "files": ["./out/**/*", "./icon.png", "./icon.ico"], "publish": [{"provider": "generic", "url": "https://lhytjhapi.blueglob.com/update"}], "win": {"icon": "./icon.ico", "artifactName": "lanhaitranslator-${version}.${ext}"}, "mac": {"icon": "./build/icon.icns", "artifactName": "lanhaitranslator-${version}.${ext}", "entitlements": "entitlements.mac.plist", "extendInfo": {"NSMicrophoneUsageDescription": "需要麦克风权限", "NSCameraUsageDescription": "需要摄像头权限", "NSScreenCaptureUsageDescription": "需要录屏权限"}, "target": [{"target": "dmg", "arch": "universal"}]}, "linux": {"icon": "./icon.png"}}}