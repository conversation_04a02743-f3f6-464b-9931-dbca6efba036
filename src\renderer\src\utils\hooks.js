import { onMounted, onUnmounted, ref, onBeforeUnmount } from 'vue'
import { debounce } from 'lodash-es'
import { getTargetElement } from 'vue-hooks-plus/lib/utils/domTarget'
export const useChatBounds = (
  option = { targeteElement, callback, targeteElementResizeObserverOption },
  duration = 100
) => {
  const targets = ref([option])
  const onWebContentViewResize = debounce((e) => {
    targets.value.forEach(({ targeteElement, callback }) => {
      console.log(targeteElement)
      let {
        offsetWidth: width,
        offsetHeight: height,
        offsetLeft: x,
        offsetTop: y
      } = getTargetElement(targeteElement)
      let bounds = { x, y, width, height }
      callback(bounds, e)
    })
  }, duration)

  const resizeObserver = new ResizeObserver(onWebContentViewResize)

  const initElementObserver = async ({
    targeteElement,
    targeteElementResizeObserverOption,
    callback
  }) => {
    resizeObserver.observe(getTargetElement(targeteElement), targeteElementResizeObserverOption)
    let {
      offsetWidth: width,
      offsetHeight: height,
      offsetLeft: x,
      offsetTop: y
    } = getTargetElement(targeteElement)
    if (width === 0 || height === 0) {
      return
    }

    let bounds = { x, y, width, height }
    callback(bounds)
  }

  onMounted(() => {
    targets.value.forEach((o) => {
      initElementObserver(o)
    })
  })

  onUnmounted(() => {
    resizeObserver.disconnect()
  })

  return {
    targets,
    resizeObserver,
    initElementObserver
  }
}


export function useEscKey(callback, delay = 500) {
  let cooldown = false

  const handler = (event) => {
    if (
      event.key === 'Escape' &&
      !event.ctrlKey &&
      !event.shiftKey &&
      !event.altKey &&
      !event.metaKey &&
      !cooldown
    ) {
      cooldown = true
      callback()
      setTimeout(() => (cooldown = false), delay)
    }
  }

  onMounted(() => {
    document.addEventListener('keydown', handler)
  })

  onBeforeUnmount(() => {
    document.removeEventListener('keydown', handler)
  })
}
