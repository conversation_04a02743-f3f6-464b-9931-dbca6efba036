<svg width="115" height="113" viewBox="0 0 115 113" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="61.7543" cy="56.5903" r="42.7524" fill="url(#paint0_linear_809_1242)"/>
<path d="M35.627 58.9648L55.8156 77.3721L0 112.999L35.627 58.9648Z" fill="url(#paint1_linear_809_1242)"/>
<g filter="url(#filter0_i_809_1242)">
<path d="M18.9705 42.0688C14.1768 45.9907 15.2099 47.8087 16.5954 48.6004C34.4089 62.2576 61.7229 51.5693 51.0348 37.9123C40.3467 24.2553 25.5022 36.7251 18.9705 42.0688Z" fill="url(#paint2_linear_809_1242)"/>
</g>
<path d="M72.2456 95.8146C68.3238 100.608 66.5057 99.5753 65.714 98.1898C52.0569 80.3763 62.7451 53.0622 76.4021 63.7503C90.0591 74.4384 77.5894 89.2829 72.2456 95.8146Z" fill="url(#paint3_linear_809_1242)"/>
<g filter="url(#filter1_d_809_1242)">
<path d="M90.5378 59.9336C114.308 34.3217 112.525 13.6906 106.804 8.2172C92.3205 -6.46687 47.751 23.7687 37.8698 44.9723C30.0301 61.7951 37.2756 72.8371 43.2182 76.9872C48.9627 81.5325 66.7674 85.5456 90.5378 59.9336Z" fill="url(#paint4_linear_809_1242)"/>
</g>
<mask id="mask0_809_1242" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="34" y="4" width="77" height="77">
<path d="M90.4909 60.0194C114.242 34.368 112.461 13.7051 106.744 8.22319C92.2722 -6.48354 47.7384 23.7986 37.8651 45.035C30.0317 61.8838 37.2713 72.9428 43.2092 77.0993C48.9491 81.6516 66.7395 85.6709 90.4909 60.0194Z" fill="#1C83FF"/>
</mask>
<g mask="url(#mask0_809_1242)">
<g filter="url(#filter2_f_809_1242)">
<path d="M74.8953 27.7375C111.116 -8.48311 150.172 -7.2641 138.43 -8.4831C108.4 -12.339 61.7515 -0.41292 38.5939 20.9635C22.3548 35.9537 12.999 71.3595 24.6274 71.1995C36.0654 71.5407 39.2279 63.4048 74.8953 27.7375Z" fill="white"/>
</g>
<g filter="url(#filter3_f_809_1242)">
<path d="M131.278 64.6966C162.187 24.8347 151.531 6.86987 139.789 5.65087C109.759 1.79501 45.23 60.0093 36.284 86.3888C29.1863 107.318 45.5755 112.681 57.2039 112.521C68.642 112.862 100.369 104.559 131.278 64.6966Z" fill="#2D81E6"/>
</g>
<g filter="url(#filter4_f_809_1242)">
<path d="M127.278 67.21C152.893 23.7561 140.049 7.28457 128.247 7.56176C104.569 39.7877 66.0936 85.402 35.7948 100.753C16.0805 110.741 48.3398 125.657 59.8545 124.027C71.2437 122.917 101.664 110.664 127.278 67.21Z" fill="white" fill-opacity="0.6"/>
</g>
</g>
<g filter="url(#filter5_di_809_1242)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M109.824 26.0401C98.3348 18.1467 90.6558 10.6763 87.104 5.67853C95.218 3.44779 102.361 3.80897 106.761 8.22319C109.601 10.9116 111.483 17.2514 109.824 26.0401Z" fill="url(#paint5_linear_809_1242)"/>
</g>
<g filter="url(#filter6_d_809_1242)">
<ellipse cx="47.4854" cy="65.9411" rx="20.1466" ry="5.02068" transform="rotate(-45 47.4854 65.9411)" fill="url(#paint6_linear_809_1242)"/>
</g>
<g filter="url(#filter7_dii_809_1242)">
<circle cx="79.5669" cy="36.2681" r="9.50053" fill="#A0D9FF"/>
</g>
<g filter="url(#filter8_di_809_1242)">
<circle cx="79.5668" cy="36.268" r="7.1254" fill="url(#paint7_linear_809_1242)"/>
<circle cx="79.5668" cy="36.268" r="7.0254" stroke="#327CDF" stroke-width="0.2"/>
</g>
<defs>
<filter id="filter0_i_809_1242" x="15.4375" y="29.7324" width="38.0051" height="25.2036" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.392778 0 0 0 0 0.638485 0 0 0 0 0.933333 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_809_1242"/>
</filter>
<filter id="filter1_d_809_1242" x="30.4395" y="0.337891" width="84.0042" height="84.0044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.116417 0 0 0 0 0.529167 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_809_1242"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_809_1242" result="shape"/>
</filter>
<filter id="filter2_f_809_1242" x="-10.5574" y="-39.2017" width="181.115" height="140.411" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_809_1242"/>
</filter>
<filter id="filter3_f_809_1242" x="-15.4385" y="-44.5317" width="216.976" height="207.063" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="25" result="effect1_foregroundBlur_809_1242"/>
</filter>
<filter id="filter4_f_809_1242" x="19.5527" y="-2.44189" width="132.577" height="136.592" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_809_1242"/>
</filter>
<filter id="filter5_di_809_1242" x="83.104" y="1.33789" width="27.3413" height="28.7021" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.258333 0 0 0 0 0.520833 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_809_1242"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_809_1242" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="-6"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.145098 0 0 0 0 0.788235 0 0 0 0 0.847059 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_809_1242"/>
</filter>
<filter id="filter6_d_809_1242" x="28.8005" y="47.2563" width="37.3699" height="37.3696" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.117333 0 0 0 0 0.533333 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_809_1242"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_809_1242" result="shape"/>
</filter>
<filter id="filter7_dii_809_1242" x="68.0664" y="22.7676" width="23.001" height="27.001" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_809_1242"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_809_1242" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_809_1242"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.686275 0 0 0 0 0.898039 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_809_1242" result="effect3_innerShadow_809_1242"/>
</filter>
<filter id="filter8_di_809_1242" x="70.4414" y="27.1426" width="18.2507" height="20.251" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.128333 0 0 0 0 0.583333 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_809_1242"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_809_1242" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_809_1242"/>
</filter>
<linearGradient id="paint0_linear_809_1242" x1="43.0502" y1="17.5787" x2="82.0617" y2="95.0674" gradientUnits="userSpaceOnUse">
<stop stop-color="#E0ECF7"/>
<stop offset="1" stop-color="#D3E4F7"/>
</linearGradient>
<linearGradient id="paint1_linear_809_1242" x1="42.1586" y1="67.2778" x2="17.8135" y2="93.4043" gradientUnits="userSpaceOnUse">
<stop stop-color="#86F0F8"/>
<stop offset="1" stop-color="#86F0F8" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_809_1242" x1="34.4401" y1="31.7324" x2="34.4401" y2="54.9359" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3E5FF"/>
<stop offset="1" stop-color="#5192E6"/>
</linearGradient>
<linearGradient id="paint3_linear_809_1242" x1="82.582" y1="80.345" x2="59.3786" y2="80.345" gradientUnits="userSpaceOnUse">
<stop stop-color="#D3E5FF"/>
<stop offset="1" stop-color="#5192E6"/>
</linearGradient>
<linearGradient id="paint4_linear_809_1242" x1="84.5952" y1="6.1165" x2="43.7355" y2="77.3435" gradientUnits="userSpaceOnUse">
<stop stop-color="#0071FF"/>
<stop offset="0.812627" stop-color="#157CF9"/>
<stop offset="1" stop-color="#116EC3"/>
</linearGradient>
<linearGradient id="paint5_linear_809_1242" x1="96.3137" y1="4.33789" x2="106.939" y2="21.6786" gradientUnits="userSpaceOnUse">
<stop stop-color="#70E4F5"/>
<stop offset="1" stop-color="#21C7E3"/>
</linearGradient>
<linearGradient id="paint6_linear_809_1242" x1="47.4854" y1="60.9204" x2="47.4854" y2="70.9617" gradientUnits="userSpaceOnUse">
<stop stop-color="#CAF4FF"/>
<stop offset="1" stop-color="#6CADF7"/>
</linearGradient>
<linearGradient id="paint7_linear_809_1242" x1="75.4103" y1="33.4324" x2="86.6922" y2="34.62" gradientUnits="userSpaceOnUse">
<stop stop-color="#74B4FC"/>
<stop offset="1" stop-color="#3D98FF"/>
</linearGradient>
</defs>
</svg>
