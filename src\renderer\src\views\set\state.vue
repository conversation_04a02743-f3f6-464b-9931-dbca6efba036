<template>
  <div class="sys_base">
    <!-- 系统导航 -->
    <!-- <div :class="showHideWord?'sys_left':'sys_left sys_left_b'">
			<Tabar :softName='chatName' @changeChat='changeChat' @showHideWordFun='showHideWordFun'></Tabar>
		</div> -->
    <!-- 主要内容区 -->
    <div
      :class="
        tabarStore.showHideWord
          ? 'main_cont contact_main_cont'
          : 'main_cont contact_main_cont small_tab'
      "
    >
      <!-- 数据统计 -->
      <div class="contact_cont">
        <div class="stateData">
          <div class="left_top_tit">
            <div class="tit">
              <h2>增长曲线图</h2>
              <span>Growth curve chart</span>
            </div>
            <div class="manyDay">
              <div class="item">
                <p>当日新增</p>
                <span>{{ lineChart.day }}</span>
              </div>
              <div class="item">
                <p>当周新增</p>
                <span>{{ lineChart.week }}</span>
              </div>
              <div class="item">
                <p>30天内新增</p>
                <span>{{ lineChart.month }}</span>
              </div>
            </div>
            <div class="selPlant">
              <div class="img">
                <img :src="icon60" alt="" />
              </div>
              <div class="select_div">
                <el-select
                  @change="changeSourceFun"
                  v-model="plantState"
                  placeholder="请选择社媒来源"
                >
                  <el-option label="所有" value="0"></el-option>
                  <el-option label="telegram" value="1"></el-option>
                  <el-option label="whatsapp" value="2"></el-option>
                  <el-option label="facebook" value="3"></el-option>
                  <el-option label="instagram" value="4"></el-option>
                  <el-option label="zalo" value="5"></el-option>
                  <el-option label="twitter" value="6"></el-option>
                  <!-- <el-option label="facebookbusienss" value="7"></el-option> -->
                  <el-option label="tiktok" value="8"></el-option>
                  <el-option label="discord" value="9"></el-option>
                  <!-- <el-option v-for="item in fansConfig.fans_sources" :label="item.name" :value="item.id"></el-option> -->
                </el-select>
              </div>
            </div>
          </div>
          <div class="left_TT">
            <div class="leftT" ref="chartContainer"></div>
          </div>
        </div>
        <div class="zzStateData">
          <div class="left_top_tit">
            <div class="tit">
              <h2>各应用增长柱状图</h2>
              <span>Application growth bar chart</span>
            </div>
            <div class="manyDay">
              <div class="item" @click="changeBarFun(1)">
                <p :class="barType == 1 ? 'on' : ''">当日</p>
              </div>
              <div class="item" @click="changeBarFun(2)">
                <p :class="barType == 2 ? 'on' : ''">当周</p>
              </div>
              <div class="item" @click="changeBarFun(3)">
                <p :class="barType == 3 ? 'on' : ''">30天内</p>
              </div>
            </div>
          </div>
          <div class="left_TT">
            <div class="leftT" ref="chartContainerZz"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Tabar from '../../components/tabar.vue'
import { ref, watch, onMounted, onUnmounted } from 'vue'
// import {
// 	getWorkOrderSql
// } from "../../api/account.js"
import * as echarts from 'echarts' // 导入Echarts 图表
import { useRouter, useRoute } from 'vue-router' // 导入 useRouter
import { getLineChartSql, getFansConfigSql, getBarChartSql } from '../../api/account.js'
import { useTabStore } from '@renderer/store/index.js'
const tabarStore = useTabStore()
const router = useRouter() // 获取路由实例
const route = useRoute() // 获取当前路由对象
// 导入本地图片
const icon55 = new URL('../../assets/img/icon55.svg', import.meta.url).href
const icon56 = new URL('../../assets/img/icon56.svg', import.meta.url).href
const icon57 = new URL('../../assets/img/icon57.svg', import.meta.url).href
const icon60 = new URL('../../assets/img/icon60.svg', import.meta.url).href

const plantState = ref('0')
// 获取粉丝配置fansConfig
const fansConfig = ref({})

function getFansConfig() {
  getFansConfigSql({}).then((response) => {
    if (response.code == 1) {
      fansConfig.value = response.data
    }
  })
}
getFansConfig()

// 窗口编号
const windowNumber = ref(1)
// 初始化 ECharts 实例
const chartContainer = ref(null)
let chartInstance = null
const legend_selected = ref({
  telegram: true,
  whatsapp: true,
  facebook: true,
  instagram: true,
  zalo: true
})
const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  chartInstance = echarts.init(chartContainer.value)
  const option = {
    color: ['#00C7F2', '#5577FF', '#8552a1', '#f391a9', '#f47920'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: [
        'telegram',
        'whatsapp',
        'facebook',
        'instagram',
        'zalo',
        'twitter',
        // 'facebookbusienss',
        'tiktok',
        'discord'
      ],
      left: 'left',
      selected: legend_selected.value,
      textStyle: {
        color: 'black'
      },
      show: true,
      tooltip: {
        show: true
      }
    },
    // toolbox: {
    // 	feature: {
    // 		saveAsImage: {}
    // 	}
    // },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: ['Jan', 'Fed', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: 'telegram',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(128, 255, 165)'
            },
            {
              offset: 1,
              color: 'rgb(1, 191, 236)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.telegram
      },
      {
        name: 'whatsapp',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: 'rgb(77, 119, 255)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.whatsapp
      },
      {
        name: 'facebook',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#8552a1'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.facebook
      },
      {
        name: 'instagram',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#f391a9'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.instagram
      },
      {
        name: 'zalo',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#f47920'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.zalo
      },
      {
        name: 'twitter',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#f47920'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.twitter
      },
      // {
      //   name: 'facebookbusienss',
      //   type: 'line',
      //   stack: 'Total',
      //   smooth: true,
      //   lineStyle: {
      //     width: 0
      //   },
      //   showSymbol: false,
      //   areaStyle: {
      //     opacity: 0.8,
      //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //       {
      //         offset: 0,
      //         color: 'rgb(0, 221, 255)'
      //       },
      //       {
      //         offset: 1,
      //         color: '#f47920'
      //       }
      //     ])
      //   },
      //   emphasis: {
      //     focus: 'series'
      //   },
      //   data: lineChart.value.facebookbusienss
      // },
      {
        name: 'tiktok',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#f47920'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.tiktok
      },
      {
        name: 'discord',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 25)'
            },
            {
              offset: 1,
              color: '#f47920'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.discord
      }
    ]
  }

  chartInstance.setOption(option)
}

// 首页 数据分析
const lineChart = ref({})
const getLineChart = () => {
  getLineChartSql({}).then((response) => {
    if (response.code == 1) {
      lineChart.value = response.data

      initChart()
    }
  })
}

// 改变来源方法 plantState.value
const changeSourceFun = () => {
  if (plantState.value == 0) {
    legend_selected.value = {
      telegram: true,
      whatsapp: true,
      facebook: true,
      instagram: true,
      zalo: true
    }
  } else if (plantState.value == 1) {
    legend_selected.value = {
      telegram: true,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false
    }
  } else if (plantState.value == 2) {
    legend_selected.value = {
      telegram: false,
      whatsapp: true,
      facebook: false,
      instagram: false,
      zalo: false
    }
  } else if (plantState.value == 3) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: true,
      instagram: false,
      zalo: false
    }
  } else if (plantState.value == 4) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: true,
      zalo: false
    }
  } else if (plantState.value == 5) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: true
    }
  } else if (plantState.value == 6) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false,
      twitter: true,
      facebookbusienss: false,
      tiktok: false,
      discord: false
    }
  } else if (plantState.value == 7) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false,
      twitter: false,
      facebookbusienss: true,
      tiktok: false,
      discord: false
    }
  } else if (plantState.value == 8) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false,
      twitter: false,
      facebookbusienss: false,
      tiktok: true,
      discord: false
    }
  } else if (plantState.value == 9) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false,
      twitter: false,
      facebookbusienss: false,
      tiktok: false,
      discord: true
    }
  }
  initChart()
}

// 获取柱状图统计
// 初始化 ECharts 实例
const chartContainerZz = ref(null)
let chartInstanceZhu = null
const initChartZhu = () => {
  if (chartInstanceZhu) {
    chartInstanceZhu.dispose()
  }
  chartInstanceZhu = echarts.init(chartContainerZz.value)
  let option = {
    tooltip: {},
    legend: {
      left: 'left'
    },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '0%',
      containLabel: true
    },
    dataset: {
      source: zhuChart.value
    },
    xAxis: { type: 'category' },
    yAxis: {},
    // Declare several bar series, each will be mapped
    // to a column of dataset.source by default.
    series: [
      { type: 'bar' },
      { type: 'bar' },
      { type: 'bar' },
      { type: 'bar' },
      { type: 'bar' },
      { type: 'bar' },
      { type: 'bar' },
      { type: 'bar' },
      // { type: 'bar' }
    ]
  }
  chartInstanceZhu.setOption(option)
}
// 首页 数据分析
const zhuChart = ref()
const barType = ref(2)
const getLineChartZhu = () => {
  getBarChartSql({ type: barType.value }).then((response) => {
    if (response.code == 1) {
      zhuChart.value = response.data
      initChartZhu()
    }
  })
}
const changeBarFun = (type) => {
  barType.value = type
  getLineChartZhu()
}
// 监听窗口编号变化
watch(windowNumber, () => {
  initChart()
})
// 监听窗口大小变化
const handleResize = () => {
  let width = window.innerWidth
  let height = window.innerHeight
  if (chartInstance && chartInstanceZhu) {
    chartInstance.resize()
    chartInstanceZhu.resize()
  }
}

onMounted(() => {
  // initChart();
  getLineChartZhu()
  getLineChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance && chartInstanceZhu) {
    chartInstance.dispose()
    chartInstanceZhu.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

// 改变平台
const changeChat = (cN) => {
  chatName.value = cN
}
// 设置是否显示设置信息页面
const is_open = ref(false)
const openSetPage = (son_b) => {
  is_open.value = son_b
}
// 传递左侧栏目隐藏或展示文字方法
const showHideWord = ref(true)
watch(
  () => route.query,
  (newParams, oldParams) => {
    // 设置左侧是否显示
    if (newParams.chat == 'slide') {
      showHideWord.value = newParams.type == 'true' ? true : false
    }
  },
  { immediate: true } // 立即执行一次，以确保初始参数也被捕获
)
</script>

<style lang="scss">
@import url('../../assets/style/contact.scss');
</style>
