/**
 * 性能优化工具集
 */

/**
 * 高性能节流函数
 * 使用 requestAnimationFrame 优化 DOM 操作
 */
export const throttle = (func, delay, options = { trailing: true, useRAF: false }) => {
  let lastTime = 0
  let timer = null
  let rafId = null
  let lastArgs = null
  let lastThis = null

  const execute = () => {
    lastTime = Date.now()
    timer = null
    rafId = null
    func.apply(lastThis, lastArgs)
  }

  return function (...args) {
    const now = Date.now()
    const remainingTime = delay - (now - lastTime)

    // 如果剩余时间 <= 0，或者从未执行过，则立即执行
    if (remainingTime <= 0 || lastTime === 0) {
      // 清除之前的定时器
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }
      
      lastTime = now
      
      if (options.useRAF) {
        rafId = requestAnimationFrame(() => func.apply(this, args))
      } else {
        func.apply(this, args)
      }
    }
    // 如果允许尾部调用，并且没有定时器在等待
    else if (options.trailing && !timer && !rafId) {
      lastArgs = args
      lastThis = this
      
      if (options.useRAF) {
        rafId = requestAnimationFrame(execute)
      } else {
        timer = setTimeout(execute, remainingTime)
      }
    }
  }
}

/**
 * 防抖函数
 */
export const debounce = (func, delay, immediate = false) => {
  let timer = null
  
  return function (...args) {
    const callNow = immediate && !timer
    
    if (timer) {
      clearTimeout(timer)
    }
    
    timer = setTimeout(() => {
      timer = null
      if (!immediate) {
        func.apply(this, args)
      }
    }, delay)
    
    if (callNow) {
      func.apply(this, args)
    }
  }
}

/**
 * 批量 DOM 操作优化器
 */
export class BatchDOMUpdater {
  constructor() {
    this.updates = []
    this.rafId = null
  }

  /**
   * 添加 DOM 更新任务
   */
  add(updateFn) {
    this.updates.push(updateFn)
    this.scheduleUpdate()
  }

  /**
   * 调度更新
   */
  scheduleUpdate() {
    if (this.rafId) return
    
    this.rafId = requestAnimationFrame(() => {
      this.flush()
    })
  }

  /**
   * 执行所有更新
   */
  flush() {
    const updates = this.updates.splice(0)
    this.rafId = null
    
    // 批量执行所有 DOM 更新
    updates.forEach(updateFn => {
      try {
        updateFn()
      } catch (error) {
        console.error('[BatchDOMUpdater] Update failed:', error)
      }
    })
  }

  /**
   * 清除所有待执行的更新
   */
  clear() {
    this.updates = []
    if (this.rafId) {
      cancelAnimationFrame(this.rafId)
      this.rafId = null
    }
  }
}

/**
 * 内存优化的观察器
 */
export class OptimizedMutationObserver {
  constructor(callback, options = {}) {
    this.callback = callback
    this.options = {
      throttleDelay: 100,
      batchSize: 50,
      ...options
    }
    
    this.observer = null
    this.mutationQueue = []
    this.isProcessing = false
    
    // 使用节流优化回调执行
    this.throttledProcess = throttle(
      this.processMutations.bind(this), 
      this.options.throttleDelay,
      { trailing: true, useRAF: true }
    )
  }

  /**
   * 开始观察
   */
  observe(target, config) {
    this.observer = new MutationObserver((mutations) => {
      this.mutationQueue.push(...mutations)
      this.throttledProcess()
    })
    
    this.observer.observe(target, config)
  }

  /**
   * 处理变更队列
   */
  processMutations() {
    if (this.isProcessing || this.mutationQueue.length === 0) return
    
    this.isProcessing = true
    
    try {
      // 分批处理变更，避免阻塞主线程
      const batch = this.mutationQueue.splice(0, this.options.batchSize)
      this.callback(batch, this.observer)
    } catch (error) {
      console.error('[OptimizedMutationObserver] Processing error:', error)
    } finally {
      this.isProcessing = false
      
      // 如果还有待处理的变更，继续处理
      if (this.mutationQueue.length > 0) {
        setTimeout(() => this.processMutations(), 0)
      }
    }
  }

  /**
   * 停止观察
   */
  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
    this.mutationQueue = []
    this.isProcessing = false
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  constructor(name = 'Unknown') {
    this.name = name
    this.marks = new Map()
    this.measures = new Map()
  }

  /**
   * 标记时间点
   */
  mark(label) {
    const markName = `${this.name}-${label}`
    performance.mark(markName)
    this.marks.set(label, performance.now())
    return this
  }

  /**
   * 测量两个时间点之间的耗时
   */
  measure(startLabel, endLabel = null) {
    const measureName = `${this.name}-${startLabel}-${endLabel || 'now'}`
    
    if (endLabel) {
      performance.measure(measureName, `${this.name}-${startLabel}`, `${this.name}-${endLabel}`)
    } else {
      performance.measure(measureName, `${this.name}-${startLabel}`)
    }
    
    const measure = performance.getEntriesByName(measureName)[0]
    this.measures.set(`${startLabel}-${endLabel || 'now'}`, measure.duration)
    
    return measure.duration
  }

  /**
   * 获取所有测量结果
   */
  getResults() {
    return {
      marks: Object.fromEntries(this.marks),
      measures: Object.fromEntries(this.measures)
    }
  }

  /**
   * 打印性能报告
   */
  report() {
    console.group(`[Performance Report] ${this.name}`)
    
    console.log('Marks:', this.marks)
    console.log('Measures:', this.measures)
    
    // 计算总耗时
    const totalTime = Array.from(this.measures.values()).reduce((sum, time) => sum + time, 0)
    console.log(`Total Time: ${totalTime.toFixed(2)}ms`)
    
    console.groupEnd()
  }

  /**
   * 清除所有数据
   */
  clear() {
    this.marks.clear()
    this.measures.clear()
    
    // 清除 Performance API 中的数据
    performance.clearMarks()
    performance.clearMeasures()
  }
}

/**
 * 创建全局性能监控器实例
 */
export const createPerformanceMonitor = (name) => new PerformanceMonitor(name)
