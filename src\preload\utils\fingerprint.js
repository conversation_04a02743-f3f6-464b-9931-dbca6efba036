/**
 * 浏览器指纹伪造工具
 * 用于防止网站检测和追踪
 */

export class FingerprintSpoofer {
  constructor(options = {}) {
    this.options = {
      enableNavigatorSpoof: true,
      enableCanvasSpoof: true,
      enableWebGLSpoof: false,
      enableScreenSpoof: false,
      enablePluginsSpoof: false,
      ...options
    }
  }

  /**
   * 初始化所有指纹伪造
   */
  init() {
    if (this.options.enableNavigatorSpoof) {
      this.spoofNavigator()
    }
    
    if (this.options.enableCanvasSpoof) {
      this.spoofCanvas()
    }
    
    if (this.options.enableWebGLSpoof) {
      this.spoofWebGL()
    }
    
    if (this.options.enableScreenSpoof) {
      this.spoofScreen()
    }
    
    if (this.options.enablePluginsSpoof) {
      this.spoofPlugins()
    }
    
    console.log('[FingerprintSpoofer] Initialized with options:', this.options)
  }

  /**
   * Navigator 信息伪装
   */
  spoofNavigator() {
    const overwrite = (obj, prop, value) => {
      Object.defineProperty(obj, prop, {
        get: () => value,
        configurable: true,
      })
    }

    // 伪装 userAgentData
    overwrite(navigator, 'userAgentData', {
      brands: [
        { brand: "Chromium", version: "137" },
        { brand: "Not/A)Brand", version: "8" }
      ],
      mobile: false,
      platform: "Windows",
      getHighEntropyValues: (hints) => {
        return Promise.resolve({
          platform: "Windows",
          platformVersion: "10.0.0",
          architecture: "x86",
          model: "",
          uaFullVersion: "137.0.0.0"
        })
      }
    })

    // 伪装语言设置
    overwrite(navigator, 'language', 'en-US')
    overwrite(navigator, 'languages', ['en-US', 'en'])
    
    // 伪装硬件信息
    overwrite(navigator, 'hardwareConcurrency', 4)
    overwrite(navigator, 'deviceMemory', 8)
  }

  /**
   * Canvas 指纹伪装
   */
  spoofCanvas() {
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL
    HTMLCanvasElement.prototype.toDataURL = function (...args) {
      try {
        const ctx = this.getContext('2d')
        this._injectCanvasNoise(ctx, this.width, this.height)
      } catch (e) {
        // 静默处理错误
      }
      return originalToDataURL.apply(this, args)
    }

    const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData
    CanvasRenderingContext2D.prototype.getImageData = function (...args) {
      this._injectCanvasNoise(this, this.canvas.width, this.canvas.height)
      return originalGetImageData.apply(this, args)
    }

    // 注入噪声的方法
    CanvasRenderingContext2D.prototype._injectCanvasNoise = function(ctx, width, height) {
      if (!ctx || typeof ctx.fillText !== 'function') return
      
      ctx.save()
      ctx.globalAlpha = 0.01
      ctx.fillStyle = '#000'
      ctx.font = '16px Arial'
      ctx.fillText(`canvas-noise-${Math.random() * 10}`, 1, height - 1)
      ctx.restore()
    }
  }

  /**
   * WebGL 指纹伪装
   */
  spoofWebGL() {
    const getParameterProxy = WebGLRenderingContext.prototype.getParameter
    WebGLRenderingContext.prototype.getParameter = function (param) {
      const spoofedParams = {
        37445: 'Fake GPU Corp.',         // UNMASKED_VENDOR_WEBGL
        37446: 'Fake GPU Model 9000',    // UNMASKED_RENDERER_WEBGL
        33901: 4096,                     // MAX_TEXTURE_SIZE
        3379: 16                         // MAX_TEXTURE_IMAGE_UNITS
      }
      
      if (param in spoofedParams) {
        return spoofedParams[param]
      }
      return getParameterProxy.call(this, param)
    }

    const getExtensionProxy = WebGLRenderingContext.prototype.getExtension
    WebGLRenderingContext.prototype.getExtension = function (name) {
      if (name === 'WEBGL_debug_renderer_info') {
        return null
      }
      return getExtensionProxy.call(this, name)
    }
  }

  /**
   * 屏幕信息伪装
   */
  spoofScreen() {
    const overwrite = (obj, prop, value) => {
      Object.defineProperty(obj, prop, {
        get: () => value,
        configurable: true,
      })
    }

    overwrite(window.screen, 'width', 1920)
    overwrite(window.screen, 'height', 1080)
    overwrite(window.screen, 'availWidth', 1920)
    overwrite(window.screen, 'availHeight', 1040)
    overwrite(window.screen, 'colorDepth', 24)
    overwrite(window.screen, 'pixelDepth', 24)
  }

  /**
   * 插件信息伪装
   */
  spoofPlugins() {
    const fakePlugin = {
      name: 'Chrome PDF Plugin',
      filename: 'internal-pdf-viewer',
      description: 'Portable Document Format',
    }

    Object.defineProperty(navigator, 'plugins', {
      get: () => [fakePlugin],
      configurable: true,
    })

    Object.defineProperty(navigator, 'mimeTypes', {
      get: () => [{
        type: 'application/pdf',
        suffixes: 'pdf',
        description: '',
        enabledPlugin: fakePlugin,
      }],
      configurable: true,
    })
  }
}

/**
 * 创建默认的指纹伪造器实例
 */
export const createDefaultSpoofer = () => {
  return new FingerprintSpoofer({
    enableNavigatorSpoof: true,
    enableCanvasSpoof: true,
    enableWebGLSpoof: false,  // 默认关闭，可能影响性能
    enableScreenSpoof: false, // 默认关闭，可能影响布局
    enablePluginsSpoof: false // 默认关闭，可能影响功能
  })
}
