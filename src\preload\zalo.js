const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');


// 接收vue 渲染层传来的数据
const api = (arg) => {
  
}



// 创建聊天库 indexDB
const createIndexDB = () => {
	let databaseName = "chatLogSel";
	let databaseVersion = 7;
	let storeName = "messages";
	let request = indexedDB.open(databaseName, databaseVersion);
	// 新建数据库
	request.onupgradeneeded = function(e) {
		const db = e.target.result;
		// 通常新建数据库以后，第一件事是新建对象仓库（即新建表），并设置主键
		var objectStore = db.createObjectStore(storeName, {
			keyPath: "id" //设置主键为 id
		});
		db.close();
	}

	request.onerror = function(event) {
		
	};
	request.onsuccess = function(event) {
	
	};

	
}
createIndexDB()

// 查询indexDb数据库是否有翻译数据
const getTranslateIndexDBDate = (translateData) => {
	return new Promise((resolve, reject) => {
		let databaseName = "chatLogSel";
		let databaseVersion = 7;
		let storeName = "messages";
		let request = indexedDB.open(databaseName, databaseVersion);

		request.onerror = function(event) {
			reject("Database error: " + event.target.errorCode);
		};

		request.onsuccess = function(event) {
			try{
				// 链接库
				const db = event.target.result;
				var transaction = db.transaction([storeName], "readwrite");
				var objectStore = transaction.objectStore(storeName);
				let indexDb_id = unescape(encodeURIComponent(translateData.text+translateData.toLang))
				const messageLog = objectStore.get(btoa(indexDb_id)); // 获取指定ID的用户数据
				messageLog.onerror = function(event) {
					// console.error("Transaction failed: " + event.target.errorCode);
				};
				messageLog.onsuccess = function(event) {
					if (messageLog.result) {
						// console.log("User: indexDB 翻译", messageLog.result); // 打印用户数据
						resolve(messageLog.result);
					} else {
						// console.log("No such user"); // 如果没有找到用户，打印消息
						resolve({});
					}
				};
				db.close();
			}catch (error) {
			
			}
		};
	})
		
}

// 添加翻译数据到indexDB数据库
const addTranslateIndexDb = (translateData)=>{
	let databaseName = "chatLogSel";
	let databaseVersion = 7;
	let storeName = "messages";
	let request = indexedDB.open(databaseName, databaseVersion);

	request.onsuccess = function(event) {
		try{
			const db = event.target.result;
			var transaction = db.transaction([storeName], "readwrite");
			var objectStore = transaction.objectStore(storeName);
			let indexDb_id = unescape(encodeURIComponent(translateData.text+translateData.toLang))
			var newItem = {
				id: btoa(indexDb_id),
				value: translateData.transText
			};
			var addRequest = objectStore.add(newItem);
		
			addRequest.onsuccess = function(event) {
				// console.log("Item added successfully");
			};
			addRequest.onerror = function(event) {
				// console.error("Error adding item: " + event.target.error.name);
			};
			db.close();
		}catch (error) {
		
		}
	};
	
	
}













// 获取ws用户的账号信息==================
let getUserInfoTimer
let mainAccountId='' //主账号
const getUserInfo = ()=>{
	getUserInfoTimer = setInterval(()=>{
		// 暂时没有找到ID
		let userid = localStorage.getItem("sh_zlast_uid");
		let username = JSON.parse(localStorage.getItem('sh_z_recentin')).displayName
		console.log(username)
		let map = {}
		console.log('usernameusernameusername',username,userid)
		if (username && userid) {
			// userInfo = JSON.parse(userInfo)
			// let userInfos = userInfo.replace(/\"/g, "")
			// let phone_arr = userInfos.split(":");
			// let phone = phone_arr[0]
			map.phone = username;
			map.userId = userid;
			map.user_id = userid;
			map.nickName = username;
			mainAccountId = userid;  //赋值主账号ID
			if(map.userId){
				ipcRenderer.sendToHost(JSON.stringify(map));
				clearInterval(getUserInfoTimer)
			}
			// getFansListFormIndexDBUser()
			getMessage()
			return
		}
	},3000)
}
getUserInfo()
// 获取未读消息---回传粉丝数据
const getMessage = () => {
	// 定时获取未读消息
	let alreay_send = ''
	// 定时获取未读消息
	setInterval(function() {
		var chats = document.querySelectorAll('#sidebarNav > div:not(#main-tab) #conversationListId [data-id="div_TabMsg_ThrdChItem"]');
		// console.log('chats preload ins', chats)
		var total = 0;
		if(chats){
			for (let chat of chats) {
				let no_read_user = chat.querySelector('[class="conv-item-body__action hasOption grid-item"]')
				if(no_read_user){
					let phone = ''
					if(chat.querySelector('[class="conv-item-title__name truncate grid-item"] [class="truncate"]')){
						phone = chat.querySelector('[class="conv-item-title__name truncate grid-item"] [class="truncate"]').innerText
					}
					let fansUserInfo = {
						'phone':phone
					}
					if(alreay_send.indexOf(phone) === -1){
						alreay_send = alreay_send+','+phone
						ipcRenderer.send("sendZaloFansList", JSON.stringify(fansUserInfo));
					}
				}
			}
		}
		
	}, 1000);
};
// 获取当前打开的会话 用户ID
let pre_sel_user_id = '';
let now_sel_user_id = '';
const getSensitiveId = () => {
	var chats = document.querySelector('#conversationListId [data-id="div_TabMsg_ThrdChItem"] [class="gridv2 conv-item conv-rel selected lv-2 fluid tiny grid-fluid-8"]');
	if (chats) {
		let activeFan = ''
		if(chats.querySelector('[class="conv-item-title__name truncate grid-item"] [class="truncate"]')){
			activeFan = chats.querySelector('[class="conv-item-title__name truncate grid-item"] [class="truncate"]').innerText;
		}
		now_sel_user_id = activeFan
		if (activeFan && pre_sel_user_id!=now_sel_user_id) {
			pre_sel_user_id = activeFan
			ipcRenderer.send("sendZaloActiveFanId", activeFan);
		}
	}
};
// 翻译相关内容 ==================================================================================
let now_ws_set = {
	engineCode:'',
	recive_lang:'',
	send_lang:''
};
const getNowSetTranslate = (map) => {
	console.log('map',map)
	if(map){
		now_ws_set.engineCode = map.engineCode?map.engineCode:''
		now_ws_set.recive_lang = map.recive_lang?map.recive_lang:''
		now_ws_set.send_lang = map.send_lang?map.send_lang:''
		
		now_ws_set.self = map.self ? map.self : false
		now_ws_set.trans_over = map.trans_over ? map.trans_over : false
		now_ws_set.self_many = map.self_many ? map.self_many : false
		now_ws_set.login_trans_his = map.login_trans_his ? map.login_trans_his : false
		now_ws_set.send_trans_zh = map.send_trans_zh ? map.send_trans_zh : false
		if(now_ws_set.send_trans_zh===true){
			now_ws_set.send_lang = "zh"
		}
	}else{
		ipcRenderer.send("empty-translate-config");
	}
	if(map.send_lang==''){
		if(document.querySelector('#myShadow')){
			document.querySelector('#myShadow').style.width = 0
			document.querySelector('#myShadow').style.height = 0
		}
	}else{
		if(document.querySelector('#myShadow')){
			document.querySelector('#myShadow').style.width = '100%'
			document.querySelector('#myShadow').style.height = '100%'
		}
	}
};
// const agent = navigator.userAgent.toLowerCase();
// const isMac = navigator.userAgentData?.platform === "macOS";
const isMac = navigator.userAgentData?.platform === "macOS" || /Mac/.test(navigator.userAgent);
// console.log("是否是isMac：" + isMac);

const scroll_bottom_chat = () =>{
	// setTimeout(function(){
	// 	let scrollDomScroll = document.querySelector('[aria-label*="的对话中的消息"] [class="x78zum5 xdt5ytf x1iyjqo2 xs83m0k x1xzczws x6ikm8r x1odjw0f x1n2onr6 xh8yej3 x16o0dkt"]')
	// 	if (scrollDomScroll) {
	// 	        // 计算元素的最大滚动高度
	// 	        var maxScrollTop = scrollDomScroll.scrollHeight - scrollDomScroll.clientHeight;
	// 	        // 将元素的scrollTop设置为其最大滚动高度，以滚动到底部
	// 			if(maxScrollTop - scrollDomScroll.scrollTop < 950){
	// 				scrollDomScroll.scrollTop = maxScrollTop;
	// 			}
	// 	} else {
	// 		// 如果元素不存在，您可能想要在这里处理错误或执行其他操作
	// 		console.error('未找到可滚动的元素');
	// 	}
	// },200)
}

// dom加载完执行函数
function onDOMContentLoaded() {
	// 确保元素存在后再添加事件监听器._ak1r .x1n2onr6 > div[data-tab="10"]
	const appElement = document.querySelector('#chatView #chat-input-content-id #richInput');
	try {
		if (appElement) {
      getSensitiveId()
    }
    if (appElement && now_ws_set.send_lang) {
      if (!document.querySelector('#myShadow')) {
        // 创建遮罩---禁止点击按钮
        var shadwoDiv = document.createElement('div')
        shadwoDiv.id = 'myShadow' // 设置 id
        document.querySelector('.normal-buttons-group').appendChild(shadwoDiv)

        var style = document.createElement('style')
        style.innerHTML = `
			
				.normal-buttons-group{
					position: relative;
				}
				#myShadow{
					position: absolute;
					width: 34px;
					height: 34px;
					z-index: 999;
					right: 0;
				}
			`
        document.head.appendChild(style)
        // // 监听消息
      }
      document.querySelector('#chat-input-content-id #richInput').addEventListener(
        'keydown',
        (event) => {
          if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
            // 设置为翻译后发送 或翻译成中文发送
            if (now_ws_set.trans_over === true || now_ws_set.send_trans_zh === true) {
              console.log('监听endter事件')
              // 获取输入框内容
              let all_cont = document.querySelectorAll('#chat-input-content-id #richInput > div')
              need_translat_cont = ''
              if (all_cont) {
                all_cont.forEach(function (element) {
                  // 打印每个元素的innerText
                  if (element) {
                    if (element.querySelector('span')) {
                      need_translat_cont =
                        need_translat_cont + element.querySelector('span').innerText
                    } else {
                      need_translat_cont = need_translat_cont + element.innerText
                    }
                  }
                })
              } else {
                need_translat_cont = document.querySelectorAll(
                  '#chat-input-content-id #richInput'
                ).innerText
              }

              // 执行你想要的操作，比如提交表单、发送消息等
              console.log('need_translat_contneed_translat_cont', need_translat_cont)
              // 翻译配置
              if (need_translat_cont != '') {
                let params = {
                  text: need_translat_cont,
                  engineCode: now_ws_set.engineCode,
                  fromLang: now_ws_set.recive_lang ? now_ws_set.recive_lang : 'auto',
                  toLang: now_ws_set.send_lang
                }
                if (now_ws_set.send_lang) {
                  // 清空whatsapp  输入框信息
                  // clear_ws_input()
                  document.querySelector('#chat-input-content-id #richInput').innerHTML = ''
                  // editStatus(false)
                  ipcRenderer
                    .invoke('getMessageTranslate', JSON.stringify(params))
                    .then((result) => {
                      console.log('线上翻译', result)
                      if (result.code === 1) {
                        // 第一个值 发送内容  第二个值是否发送
                        inputMessage(result.data.result, true)

                        addTranslateIndexDb({
                          text: result.data.result,
                          toLang: now_ws_set.recive_lang,
                          transText: need_translat_cont
                        })
                      } else {
                        inputMessage(need_translat_cont, false)
                      }
                    })
                    .catch((error) => {
                      console.error(error)
                    })
                  // 发送消息给主进程进行翻译
                  // getTranslateIndexDBDate({'text':need_translat_cont,'toLang':now_ws_set.send_lang}).then(transOverInfo => {
                  // 	if(transOverInfo.value){
                  // 		console.log('本地翻译',transOverInfo.value)
                  // 		inputMessage(transOverInfo.value, true)
                  // 	}else{
                  // 		ipcRenderer
                  // 			.invoke("getMessageTranslate", JSON.stringify(params))
                  // 			.then((result) => {
                  // 					console.log('线上翻译',result)
                  // 				if (result.code === 1) {
                  // 					// 第一个值 发送内容  第二个值是否发送
                  // 					inputMessage(result.data.result, true)

                  // 					addTranslateIndexDb({'text':need_translat_cont,'toLang':now_ws_set.send_lang,'transText':result.data.result})
                  // 				}else{
                  // 					inputMessage(need_translat_cont, false)
                  // 				}
                  // 			})
                  // 			.catch((error) => {
                  // 				console.error(error);
                  // 			});
                  // 	}

                  // })
                  // .catch(error => {
                  // 	console.error('Error:', error);
                  // });
                } else {
                  document.querySelector('.send-msg-btn').click()
                }
              }
            } else {
              // 直接发送消息--未设置
              document.querySelector('.send-msg-btn').click()
            }
            event.preventDefault()
            // 阻止事件冒泡到父元素
            event.stopPropagation()
            event.stopImmediatePropagation()
          }
        },
        true
      )
      document.querySelector('#myShadow').addEventListener(
        'click',
        function () {
          // 获取输入框内容
          let all_cont = document.querySelectorAll('#chat-input-content-id #richInput > div')
          need_translat_cont = ''
          if (all_cont) {
            all_cont.forEach(function (element) {
              // 打印每个元素的innerText
              if (element) {
                if (element.querySelector('span')) {
                  need_translat_cont = need_translat_cont + element.querySelector('span').innerText
                } else {
                  need_translat_cont = need_translat_cont + element.innerText
                }
              }
            })
          } else {
            need_translat_cont = document.querySelectorAll(
              '#chat-input-content-id #richInput'
            ).innerText
          }
          // 翻译配置
          if (need_translat_cont != '') {
            let params = {
              text: need_translat_cont,
              engineCode: now_ws_set.engineCode,
              fromLang: now_ws_set.recive_lang ? now_ws_set.recive_lang : 'auto',
              toLang: now_ws_set.send_lang
            }
            // 判断是否有发送翻译配置
            if (
              now_ws_set.send_lang &&
              (now_ws_set.self === true || now_ws_set.login_trans_his === true)
            ) {
              // 清空whatsapp  输入框信息
              document.querySelector('#chat-input-content-id #richInput').innerHTML = ''
              // 发送消息给主进程进行翻译
              ipcRenderer
                .invoke('getMessageTranslate', JSON.stringify(params))
                .then((result) => {
                  console.log('线上翻译', result)
                  if (result.code === 1) {
                    // 第一个值 发送内容  第二个值是否发送
                    inputMessage(result.data.result, true)

                    addTranslateIndexDb({
                      text: result.data.result,
                      toLang: now_ws_set.recive_lang,
                      transText: need_translat_cont
                    })
                  } else {
                    inputMessage(need_translat_cont, false)
                  }
                })
                .catch((error) => {
                  console.error(error)
                })
              // getTranslateIndexDBDate({'text':need_translat_cont,'toLang':now_ws_set.send_lang}).then(transOverInfo => {
              // 	if(transOverInfo.value){
              // 		inputMessage(transOverInfo.value, true)
              // 	}else{
              // 		ipcRenderer
              // 			.invoke("getMessageTranslate", JSON.stringify(params))
              // 			.then((result) => {
              // 				if (result.code === 1) {
              // 					// 第一个值 发送内容  第二个值是否发送
              // 					inputMessage(result.data.result, true)
              // 					addTranslateIndexDb({'text':need_translat_cont,'toLang':now_ws_set.send_lang,'transText':result.data.result})
              // 				}else{
              // 					inputMessage(need_translat_cont, false)
              // 				}
              // 			})
              // 			.catch((error) => {
              // 				console.error(error);
              // 			});
              // 	}

              // })
              // .catch(error => {
              // 	console.error('Error:', error);
              // });
            } else {
              document.querySelector('.send-msg-btn').click()
            }
          } else {
            document.querySelector('[data-translate-title="STR_TOOLTIP_EMOJI"]').click()
          }
        },
        { once: true }
      )
    }
	} catch (error) {
		
	}
	setTimeout(function() {
		onDOMContentLoaded()
	}, 2000)
}
// 监听 DOMContentLoaded 事件
document.addEventListener('DOMContentLoaded', onDOMContentLoaded);
// 把翻译内容输入到输入框并点击发送
function inputMessage(in_cont, is_send=false){
    let element = document.querySelector('#chat-input-content-id #richInput');
	element.setAttribute('contenteditable','true')
    element.focus();
	
	document.execCommand('insertText', false, in_cont);
	setTimeout(function(){
		document.querySelector('.send-msg-btn').click()
	},50);
	// console.log('翻译内容录入',in_cont)
 //    // 模拟输入事件
 //    const textToInsert = in_cont;
 //    const inputEvent = new InputEvent('input', {
 //      bubbles: true,
 //      cancelable: true,
 //      data: textToInsert,
 //      inputType: 'insertText'
 //    });
 //    element.dispatchEvent(inputEvent);
 //    element.keydown = null;
 //    element.onkeydown = null;
	// if(is_send){
	// 	// setTimeout(function(){
	// 	// 	document.querySelector('.send-msg-btn').click()
	// 	// },50);
	// }
    

 //    editStatus(true)
}
// 清空whatsapp 输入框方法
function clear_ws_input(){
	let keyDownA ={}
	if(isMac){
		keyDownA = {
			key: 'a',
			metaKey: true,
			bubbles: true,
			cancelable: true
		}
	}else{
		keyDownA = {
			key: 'a',
			ctrlKey: true,
			bubbles: true,
			cancelable: true
		}
	}
	console.log('keydownA', keyDownA)
	const selectAllEvent = new KeyboardEvent('keydown',keyDownA);
	// 创建模拟删除操作的事件（这里还是以Backspace为例，也可以尝试Delete键等）
	const deleteEvent = new KeyboardEvent('keydown', {
	    key: 'Backspace',
	    bubbles: true,
	    cancelable: true
	});
	let element = document.querySelector('#richInput')
	console.log('清空内容',element)
	// 先触发全选操作
	setTimeout(function(){
		element.dispatchEvent(selectAllEvent);
		element.dispatchEvent(deleteEvent);
	},1000)
}
// 禁用方法-- 禁止用户输入数据
function editStatus(editStatus) {
  let element = document.querySelector('#chat-input-content-id #richInput');
  if (editStatus) {
    element.removeAttribute('contenteditable');
    element.setAttribute('contenteditable', 'true');
  } else {
    element.removeAttribute('contenteditable');
    element.setAttribute('contenteditable', 'false');
  }
}

// 循环do--翻译一聊天过的内容
function get_message_list(){
	const appElementss = document.querySelector('#chatView #messageView');
	if (appElementss  && now_ws_set.recive_lang && now_sel_user_id) {
		let all_div = appElementss.querySelectorAll('#messageViewContainer #messageViewScroll .chat-item')
		all_div.forEach(function(element) {
			// 接收信息
			try {
				let son_dom = element.querySelector('[data-component="text-container"]')
				if(son_dom){
					let in_message = son_dom.innerHTML
					if (in_message.indexOf('----------------------------') !== -1) {
					  in_message = ''
					} else {
					  in_message = son_dom.querySelector('.text').innerText
					}
					let in_time = son_dom.getAttribute('id')
					// 排至翻译信息
					let params = {
							text: in_message,
							engineCode: now_ws_set.engineCode,
							fromLang: 'auto',
							toLang: now_ws_set.recive_lang,
							
							time:in_time,// 接收时间
							type:'in',// 接收a
							ccountId:mainAccountId,
							fansId:now_sel_user_id,
							platform:'zalo'
						};
					// 判断是否有收到的信息需要翻译
					if(in_message){
						
						getTranslateIndexDBDate({'text':in_message,'toLang':now_ws_set.recive_lang}).then(transOverInfo => {
							// 在这里处理查询结果
							if(transOverInfo.value){
								let string = ''
								if(in_message == transOverInfo.value){
									string = '<div>'+in_message+'</div>'
									string = string + '<div style="display:none">----------------------------</div>'
								}else{
									string = '<div>'+in_message+'</div>'
									string = string + '<div>----------------------------</div>'
									string = string + '<div>'+transOverInfo.value+'</div>'
								}
								son_dom.innerHTML = string
								son_dom.setAttribute('calss','')
								
								// 保存聊天记录
								let saveDate = params;
								saveDate.translateText = transOverInfo.value
								ipcRenderer.send("getSaveChatLog", JSON.stringify(saveDate));
								scroll_bottom_chat()
							}else{
								ipcRenderer
									.invoke("getMessageTranslate", JSON.stringify(params))
									.then((result) => {
												
										if (result.code === 1) {
											if(in_message == result.data.result){
												string = '<div>'+in_message+'</div>'
												string = string + '<div style="display:none">----------------------------</div>'
											}else{
												string = '<div>'+in_message+'</div>'
												string = string + '<div>----------------------------</div>'
												string = string + '<div>'+result.data.result+'</div>'
											}
											son_dom.innerHTML = string
											son_dom.setAttribute('calss','')
											addTranslateIndexDb({'text':in_message,'toLang':now_ws_set.recive_lang,'transText':result.data.result})
											scroll_bottom_chat()
										}else if(result.code === 400010){
												now_ws_set = {
													engineCode:now_ws_set.engineCode,
													recive_lang:'',
													send_lang:now_ws_set.send_lang
												};
											}
									})
									.catch((error) => {
										console.error(error);
									});
							}
						})
						.catch(error => {
							console.error('Error:', error);
						});
							
						
						
					}
				
				}
				// ===================
			} catch (error) {
				// console.log('接收消息报错',error)
			}

			
		});

	}
	setTimeout(function() {
		get_message_list()
	}, 2000)
}

get_message_list()
// 设置立即翻译---输入内容到输入框
function quick_message(sendInfo){
	let element = document.querySelector('#chat-input-content-id #richInput');
	element.setAttribute('contenteditable','true')
	element.focus()
	
	console.log('endInfo.message)endInfo.message)',sendInfo.message)

	document.execCommand('insertText', false, sendInfo.message);
	
	if(sendInfo.type=='send'){
		setTimeout(function(){
			document.querySelector('.send-msg-btn').click()
		},50)
	}
}
// 暴露出来预加载进程的方法
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('ipcRenderer', ipcRenderer)
    contextBridge.exposeInMainWorld('api', api) // 测试接口
	contextBridge.exposeInMainWorld('getUserInfo', getUserInfo)
	contextBridge.exposeInMainWorld('getNowSetTranslate', getNowSetTranslate)
	contextBridge.exposeInMainWorld('quick_message', quick_message)
  } catch (error) {
    console.error(error)
  }
} else {
  window.electron = ipcRenderer
  window.api = api // 测试接口
window.getUserInfo = getUserInfo
window.getNowSetTranslate = getNowSetTranslate
window.quick_message = quick_message
}
