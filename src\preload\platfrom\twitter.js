import { Platform } from '.'

const f = {
  userElSelector: 'nav>a:last-of-type',
  inputElSelector: 'div.DraftEditor-editorContainer>div',
  inputTextElSelector: 'div.public-DraftStyleDefault-block>span[data-offset-key]>span',

  sessionUserNameElSelector:
    'div[class="css-146c3p1 r-dnmrzs r-1udh08x r-1udbk01 r-3s2u2q r-bcqeeo r-1ttztb7 r-qvutc0 r-37j5jr r-a023e6 r-rjixqe r-16dba41 r-18u37iz r-1wvb978"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',
  currentSessionUserNameElSelector: `.r-x572qd div[class="css-146c3p1 r-dnmrzs r-1udh08x r-1udbk01 r-3s2u2q r-bcqeeo r-1ttztb7 r-qvutc0 r-37j5jr r-a023e6 r-rjixqe r-16dba41 r-18u37iz r-1wvb978"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]`,
  messageListElSelector:
    "[class='css-175oi2r r-16y2uox r-10m9thr r-1h0z5md r-f8sm7e r-13qz1uu r-3pj75a r-1ye8kvj']",

  unreadElSelector: '.r-l5o3uw',
  sessionElSelector: 'div[role="tablist"]',
  sendButtonElSelector:
    'button[class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1ez5h0i r-2yi16 r-1qi8awa r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l"]',
  sendButtonContainerElSelector: `[class="css-175oi2r r-1awozwy r-1sw30gj r-1867qdf r-18u37iz r-l00any r-jusfrs r-tuq35u r-1h0ofqe"]`,
  reciveMessageElSelector:
    'div[class="css-175oi2r r-1habvwh r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',

  sendMessageElSelector:
    'div[class="css-175oi2r r-obd0qt r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]'
}
export class TwitterHandler extends Platform {
  constructor(platform) {
    super(platform)
  }

  async getUserId() {
    if (!this.userId) {
      let userEl = document.querySelector(f.userElSelector)
      if (userEl) {
        this.userId = this.userName = userEl.href.split('https://x.com/').join('')
        let params = {
          userId: this.userId,
          platform: this.platform,
          phone: this.userId,
          nickName: this.userName,
          session_id: this.viewSessionId
        }
        this.sendPlatformUserInfo(params)
      }
    }
  }
  userId
  chatUserId
  userName

  async init(translater) {
    await super.init(translater)
    this.getUserId()
    this.bindInputFunction()
  }
  _o(mutations, observer) {
    if (/\/messages\/?/.test(location.pathname)) {
      // 绑定 input
      this.bindInputFunction()
      // 聊天页面
      this.translateList()
      // 获取未读消息的会话
      this.getUnreadSessionUserToFans()
      // 获取主账号信息
      this.getUserId()
      this.getCurrentSessionUserId()
    }
  }

  _u(location) {
    this.getCurrentSessionUserId()
  }

  getCurrentSessionUserId() {
    let count = 0
    const func = () => {
      let currentSessionUserNameEl = document.querySelector(f.currentSessionUserNameElSelector)
      if (currentSessionUserNameEl && count < 5) {
        if (this.chatUserId !== currentSessionUserNameEl.innerText) {
          const m = /(?<=@).*/.exec(currentSessionUserNameEl.innerText)
          this.chatUserId = m[0]
        } else {
          return
        }
        let params = {
          mainAccount: this.userId,
          fansId: this.chatUserId,
          nickName: this.chatUserId,
          platform: this.platform
        }
        this.sendCurrentSessionFansInfo(params)
      } else {
        setTimeout(() => {
          count++
          window.requestAnimationFrame(func)
        }, 500)
      }
    }
    window.requestAnimationFrame(func)
  }

  getUnreadSessionUserToFans() {
    const messageItemElList = document.querySelectorAll(f.sessionElSelector)
    let unreadCount = 0
    const unreadListInfo = []
    messageItemElList.forEach((sEl) => {
      const unreadEl = sEl.querySelector(f.unreadElSelector)
      if (unreadEl) {
        unreadCount += 1
        if (unreadEl.hasAttribute('aira-isread')) {
          return
        } else {
          unreadEl.setAttribute('aira-isread', 'true')

          const m = /(?<=@).*/.exec(sEl.querySelector(f.sessionUserNameElSelector)?.innerText)
          let nickName = m[0]
          unreadListInfo.push({
            id: nickName,
            nickName
          })
        }
      }
    })
    if (unreadCount > 0) {
      this.sendUnReadCount(unreadCount)
    }
    if (unreadListInfo.length > 0) {
      this.sendNewFansList({
        viewSessionId: this.viewSessionId,
        platform: this.platform,
        mainAccount: this.userId,
        unreadListInfo
      })
    }
  }
  bindInputFunction() {
    const func = (timestamp) => {
      const inputEL = document.querySelector(f.inputElSelector)
      if (inputEL) {
        inputEL.addEventListener(
          'keydown',
          async (event) => {
            this.createMaskDiv()
            inputEL.setAttribute('aria-bind', 'true')
            if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
              console.log(event)
              event.stopPropagation()
              event.stopImmediatePropagation()
              this.changeInputEditStatus(false, f.inputElSelector)
              let config = this.translater.config
              const isTranslate = config && config.trans_over
              const v = event.target.outerText
              if (isTranslate && v.trim()) {
                let tV = await this.translater.translateInput(v)
                console.log('tv', tV)

                tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, true)
              } else {
                await this.inputMessage(v, true)
              }

              this.switchShadowState(false)
            }
          },
          {
            capture: true
          },
          true
        )
      } else {
        setTimeout(() => {
          window.requestAnimationFrame(func)
        }, 500)
      }
    }
    window.requestAnimationFrame(func)
  }

  sendMessageToInput({ type, message }) {
    this.inputMessage(message, type === 'send')
  }

  inputMessage(value, is_send = false) {
    this.changeInputEditStatus(true, f.inputElSelector)
    return new Promise((resolve) => {
      let element = document.querySelector(f.inputElSelector)
      if (element) {
        element.focus()
        let textEl = element.querySelector(f.inputTextElSelector)
        if (textEl) {
          textEl.innerText = value
          element.dispatchEvent(
            new Event('input', {
              bubbles: true,
              data: value,
              inputType: 'insertText'
            })
          )
        } else {
          const clipboardData = new DataTransfer()
          clipboardData.setData('text/plain', value)
          const pasteEvent = new ClipboardEvent('paste', {
            bubbles: true,
            cancelable: true,
            clipboardData
          })
          element.dispatchEvent(pasteEvent)
        }
        if (is_send) {
          setTimeout(() => {
            document
              .querySelector(f.sendButtonElSelector)
              ?.dispatchEvent(new MouseEvent('click', { bubbles: true, view: window }))
            resolve()
          }, 50)
        }
      }
    })
  }

  switchShadowState(flag) {
    let m = document.querySelector('#myShadow')
    if (!m) {
      return
    }

    if (flag) {
      m.style.display = 'block'
    } else {
      m.style.display = 'none'
    }
  }

  createMaskDiv() {
    const maskDiv = document.querySelector('#myShadow')
    if (!maskDiv) {
      // 创建遮罩---禁止点击按钮
      let shadwoDiv = document.createElement('div')
      shadwoDiv.id = 'myShadow'
      shadwoDiv.style.position = 'absolute'
      shadwoDiv.style.width = '68px'
      shadwoDiv.style.height = '40px'
      shadwoDiv.style.top = '0px'
      shadwoDiv.style.right = '0px'
      // shadwoDiv.style.background = 'red'
      shadwoDiv.style.zIndex = 9999
      let maskContainer = document.querySelector(f.sendButtonContainerElSelector)
      if (maskContainer) {
        maskContainer.style.position = 'relative'
        maskContainer.appendChild(shadwoDiv)
      } else {
        return
      }

      shadwoDiv.addEventListener('click', async (event) => {
        event.stopPropagation()
        this.changeInputEditStatus(false, f.inputElSelector)
        let config = this.translater.config
        const isTranslate = config && config.trans_over
        const inputEl = document.querySelector(f.inputElSelector)

        const v = inputEl?.outerText
        console.log(v)
        if (isTranslate && v.trim()) {
          let tV = await this.translater.translateInput(v)
          tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, true)
        } else {
          await this.inputMessage(v, true)
        }
        this.switchShadowState(false)
      })
    } else {
      this.switchShadowState(true)
    }
  }

  changeInputEditStatus(editStatus, selector) {
    try {
      let element = document.querySelector(selector)

      if (!element) return
      if (editStatus) {
        element.setAttribute('contenteditable', 'true')
      } else {
        element.setAttribute('contenteditable', 'false')
      }
    } catch (error) {
      console.error(error)
    }
  }

  async translateList() {
    let listEL = document.querySelector(f.messageListElSelector)
    if (listEL) {
      const reciveMessageElList = listEL.querySelectorAll(f.reciveMessageElSelector)
      reciveMessageElList.forEach((el, index) => {
        this.translater.translateMessage(el, { type: 'in' })
      })

      const sendMessageElList = listEL.querySelectorAll(f.sendMessageElSelector)
      sendMessageElList.forEach((el, index) => {
        this.translater.translateMessage(el, { type: 'out' })
      })
    }
  }
}
