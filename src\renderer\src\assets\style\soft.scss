.sys_base {
  .sys_left_b {
    width: 85px;
  }
  .small_tab {
    margin-left: 102px !important;
    width: calc(100% - 112px) !important;
    .webCont {
      width: calc(100vw - 110px) !important;
    }
  }
}
.soft_main_cont {
  display: flex;
  .soft_menu {
    width: 100px;
    height: 100%;
    border-radius: 10px;
    margin-right: 10px;
    background-color: var(--bg-color-cont);
  }
  .soft_set {
    width: 100px;
    // transition: all 0.2s;
    background-color: var(--bg-color-cont);
    border-radius: 10px;
  }
  .open_soft_set {
    width: 450px !important;
  }
  .webCont {
    width: calc(100vw - 220px);
    border-radius: 10px;
    overflow: hidden;
    // transition: all 0.1s;
    margin-right: 10px;
    background-color: var(--bg-color-cont);
    .webview {
      width: 100%;
      height: 100%;
    }
  }
  .open_webCont {
    width: calc(100vw - 570px);
  }
}
