import TabViewApi from './viewApi.js'
import AppApi from './appApi.js'
import TransalteApi from './translateApi.js'

import { getAllMethods } from '../utils/index.js'

export function mountIpcApi() {
  const APIARR = [new TabViewApi(), new AppApi(), new TransalteApi()]

  // 监听ipc事件
  APIARR.forEach((apiInstance) => {
    getAllMethods(apiInstance).forEach((method) => {
      apiInstance[method]()
    })
  })
}
