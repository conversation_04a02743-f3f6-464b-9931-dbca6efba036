.sys_menu {
  height: 100%;
  overflow-y: hidden;

  .item {
    cursor: pointer;
    width: calc(100% - 0px);
    margin-bottom: 20px;

    .name {
      height: 61px;
      display: flex;
      border-radius: 10px;
      align-items: center;
      padding: 0 10px;
      background-color: var(--bg-color-left-menu);
      color: rgba(143, 149, 178, 1);

      img {
        width: 40px;
        height: 40px;
      }

      span {
        font-family: Lato;
        font-size: 16px;
        font-weight: 700;
        line-height: 26.67px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        padding-left: 10px;
        width: calc(100% - 50px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .sonItems {
      .name {
        margin-top: 6px;
        height: 45px;
        text-align: center;
        display: flex;
        align-items: center;
        padding-left: 14px;
        justify-content: center;
        
          .w-1\/2 {
            img {
              width: 100%;
              height: 100%;
            }
          }
        img {
          width: 25px;
          height: 25px;
        }

        span {
          width: auto;
        }
      }

      .name.on {
        color: var(--font-color-left-menu-select);
        background-color: var(--bg-color-left-menu-select);
      }
    }
  }

    .item.on>.name {
    color: var(--font-color-left-menu-select);
    background-color: var(--bg-color-left-menu-select);
  }
}

.show_hide {
  position: absolute;
  bottom: 20px;
  display: flex;
  align-items: center;
  // padding:0 40px;
  justify-content: center;
  width: calc(100% - 20px);
  color: rgba(143, 149, 178, 1);

  img {
    width: 40px;
    height: 40px;
  }

  span {
    font-family: Lato;
    font-size: 18.67px;
    font-weight: 700;
    line-height: 26.67px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    padding-left: 10px;
    // width: calc(100% - 50px);
    overflow: hidden;
    white-space: nowrap;
    display: inline-block;
    text-overflow: ellipsis;
  }
}