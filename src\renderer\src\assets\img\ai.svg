<svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_205_1482)">
<circle cx="48" cy="40" r="32" fill="white"/>
</g>
<path d="M48.6221 21L32 30.5956V49.7891L48.6244 59.3847L65.2465 49.7891V30.5956L48.6221 21Z" fill="#68D6AB"/>
<path d="M40.6555 47.3346H42.9555V49.6346H40.6555V47.3346ZM45.2555 47.3346H47.5555V49.6346H45.2555V47.3346ZM49.8555 47.3346H52.1555V49.6346H49.8555V47.3346ZM54.4555 47.3346H56.7555V49.6346H54.4555V47.3346Z" fill="white"/>
<path d="M52.0221 43.8846H49.6761L48.5169 40.6048H43.45L42.3368 43.8846H40L44.8254 31H47.2335L52.0221 43.8846ZM47.9511 38.8614L46.164 33.7232C46.089 33.4575 46.0321 33.187 45.9938 32.9136H45.957C45.9018 33.2563 45.842 33.5254 45.7776 33.7232L44.0066 38.8614H47.9534H47.9511ZM55.9827 43.8846H53.7816V31H55.9827V43.8846Z" fill="white"/>
<defs>
<filter id="filter0_d_205_1482" x="0" y="0" width="96" height="96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.560784 0 0 0 0 0.584314 0 0 0 0 0.698039 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_205_1482"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_205_1482" result="shape"/>
</filter>
</defs>
</svg>
