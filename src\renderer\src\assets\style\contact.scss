.sys_base {
  .sys_left_b {
    width: 85px;
  }
  .small_tab {
    margin-left: 102px !important;
    // width: calc(100% - 0px)!important;
    .contact_cont {
      width: calc(100% - 0px) !important;
    }
  }
}
.contact_main_cont {
  display: flex;
  height: 100%;
  width: calc(100% - 122px) !important;
  .add_btn {
    color: #000;
    line-height: 40px;
    background-color: #f00;
  }
  .contact_cont {
    width: calc(100% - 110px) !important;
    border-radius: 10px;
    overflow-y: auto;
    background-color: var(--bg-color-cont);
    // transition: all 0.3s;
    padding: 20px;
    .tit {
      h2 {
        font-family: Nunito Sans;
        font-size: 24px;
        font-weight: 700;
        line-height: 32px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: var(--font-color-login);
      }
      span {
        font-family: Nunito Sans;
        font-size: 16px;
        font-weight: 400;
        line-height: 21.33px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #dddfe7;
      }
    }
    .conts {
      height: calc(100% - 60px);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 654px;
        height: 584px;
      }
      .item {
        display: flex;
        line-height: 40px;
        height: 40px;
        background-color: var(--bg-color-index-echars);
        align-items: center;
        width: 256px;
        margin: 15px auto;
        justify-content: center;
        border-radius: 50px;
        color: var(--font-color-contact);
        img {
          width: 17px;
          height: 17px;
        }
        span {
          padding-left: 10px;
        }
      }
    }
    // 系统设置
    .sys_set {
      .items {
        padding: 20px 0;
        h3 {
          font-family: Inter;
          font-size: 20px;
          font-weight: 400;
          line-height: 25px;
          letter-spacing: 0.035em;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #8f95b2;
        }
        .setList {
          padding-top: 10px;
          .item {
            display: flex;
            align-items: center;
            p {
              width: 24px;
              height: 24px;
              border: 1px solid var(--border-color-translate);
              border-radius: 4px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 10px;
              margin-top: 5px;
            }
            span {
              color: #8f95b2;
              font-size: 24px;
            }
          }
          .item.on {
            p {
              background-color: #2072f7;
              border: 1px solid #2072f7;
            }
            span {
              color: #2072f7;
            }
          }
        }
      }
    }

    .version {
      display: flex;
      align-items: center;
      padding: 45px 0 0 10px;
      .version_num {
        font-family: Inter;
        font-weight: 600;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: 0px;
        color: var(--font-color-login);
        padding-right: 60px;
      }
      p {
        color: #2072f7;
        font-family: Inter;
        font-weight: 600;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: 0px;
      }
    }

    // 数据统计--折线
    .stateData {
      background-color: var(--bg-color-cont);
      // width: calc(100% - 620px);
      box-shadow: 0px 0px 3px #8f95b2;
      width: 100%;
      height: 60%;
      min-height: 600px;
      margin-right: 20px;
      border-radius: 10px;
      padding: 20px;
      .left_top_tit {
        display: flex;
        justify-content: space-between;
        padding-bottom: 30px;
        .tit {
          h2 {
            font-family: Nunito Sans;
            font-size: 24px;
            font-weight: 700;
            line-height: 32px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: var(--font-color-login);
          }
          span {
            font-family: Nunito Sans;
            font-size: 16px;
            font-weight: 400;
            line-height: 21.33px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: rgba(143, 149, 178, 1);
          }
        }
        .manyDay {
          display: flex;
          align-items: center;
          color: var(--font-color-login);
          text-align: center;
          .item {
            margin: 0 20px;
            p {
              font-family: Nunito Sans;
              font-size: 16px;
              font-weight: 400;
              line-height: 20px;
              text-align: left;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
              color: #8f95b2;
              padding-bottom: 10px;
            }
            span {
              font-family: Nunito Sans;
              font-size: 24px;
              font-weight: 700;
              line-height: 24px;
              text-align: left;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
            }
          }
        }
        .selPlant {
          display: flex;
          align-items: center;
          background-color: var(--bg-color-index-echars);
          padding: 0 20px;
          border-radius: 40px;
          .img {
            width: 24px;
            height: 24px;
            img {
              width: 24px;
              height: 24px;
            }
          }
          .select_div {
            width: 160px;
            padding-left: 8px;
            .el-select__wrapper {
              height: 45px;
              border: 1px solid var(--bg-color-index-echars);
              outline: 1px solid var(--bg-color-index-echars);
              background: var(--bg-color-index-echars);
              box-shadow: none;
              font-size: 18px;
              .el-select__placeholder {
                color: var(--font-color-index-echars);
              }
            }
            .el-select__wrapper.is-focused {
              box-shadow: none;
            }
          }
        }
      }
      .left_TT {
        width: 100%;
        height: calc(100% - 90px);
        .leftT {
          width: 100%;
          height: 100%;
          border-radius: 10px;
        }
      }
    }
    // 数据统计--柱状
    .zzStateData {
      background-color: var(--bg-color-cont);
      // width: calc(100% - 620px);
      box-shadow: 0px 0px 3px #8f95b2;
      width: 100%;
      height: calc(40% - 20px);
      min-height: 400px;
      margin-right: 20px;
      border-radius: 10px;
      padding: 20px;
      margin-top: 20px;
      .left_top_tit {
        display: flex;
        justify-content: space-between;
        padding-bottom: 30px;
        .tit {
          h2 {
            font-family: Nunito Sans;
            font-size: 24px;
            font-weight: 700;
            line-height: 32px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: var(--font-color-login);
          }
          span {
            font-family: Nunito Sans;
            font-size: 16px;
            font-weight: 400;
            line-height: 21.33px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: rgba(143, 149, 178, 1);
          }
        }
        .manyDay {
          display: flex;
          align-items: center;
          color: var(--font-color-login);
          text-align: center;
          .item {
            margin: 0 20px;
            p {
              line-height: 35px;
              height: 35px;
              width: 120px;
              background-color: var(--bg-color-index-echars);

              font-family: Nunito Sans;
              font-size: 18.67px;
              font-weight: 400;
              color: var(--font-color-login);
              text-align: center;
              border-radius: 30px;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
            }
            p.on {
              background-color: #2072f7;
              color: #fff;
            }
          }
        }
      }
      .left_TT {
        width: 100%;
        height: calc(100% - 90px);
        .leftT {
          width: 100%;
          height: 100%;
          border-radius: 10px;
        }
      }
    }
  }
}
