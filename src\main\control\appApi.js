import { ipcMain, app, BrowserWindow, webContents } from 'electron'
import { GlobalObject } from '../global'
import Config from '../config/index'
import { platform } from '@electron-toolkit/utils'
import axios from 'axios'
export default class AppApi {
  getAllViews() {
    ipcMain.handle('getAllViews', (_event) => {
      let o = BrowserWindow.getAllWindows()
      console.log(o)
      let w = webContents.getAllWebContents()
      console.log(w)

      return ''
    })
  }

  getPlatform() {
    ipcMain.handle('getPlatform', (_event) => {
      return platform
    })
  }

  initOnUnReadCount() {
    ipcMain.on('setUnReadCount', (_event, params) => {
      try {
        GlobalObject.messageManager.setMessageCount(params)
      } catch (error) {
        console.warn('未读消息接受事件错误:', error)
      }
    })
  }
  sendToMainSySOver() {
    // 关闭应用--全屏应用
    ipcMain.on('sendToMainSySOver', (_event, arg) => {
      arg ? GlobalObject.mainWindow.maximize() : GlobalObject.mainWindow.unmaximize()
    })
  }

  sendToSySMin() {
    // 最小化窗口 win.minimize();
    ipcMain.on('sendToSySMin', (_event) => {
      GlobalObject.mainWindow.minimize()
    })
  }

  getAiIsSet() {
    ipcMain.handle("getAiIsSet", async (e, params) => {
      try {
        var config = {
          method: 'post',
          url: 'http://aikf.lhyq360.com/app/ai/embedding/getDateset',
          headers: {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Host': 'aikf.lhyq360.com',
            'Connection': 'keep-alive'
          },
          data: JSON.stringify(params)
        };
        const res = await axios(config)
        console.log(res);
        
        return res.data?.data?.roleId ? true : false
      } catch (error) {
        console.error(error)
      }
    })
  }
  sysSet() {
    // 获取关闭是否最小化到托盘
    ipcMain.on('sysSet', (_event, data, type) => {
      if (type == 'win') {
        Config.iscloseApp = data
      } else if (type == 'dark') {
        GlobalObject.mainWindow.webContents.send('reloadIconUrl', data)
      } else if (type === 'message') {
        Config.isShowMessage = data
        if (!GlobalObject.messageManager) {
          return
        }
        if (data) {
          GlobalObject.messageManager.showMessageCount()
        } else {
          GlobalObject.messageManager.hideMessageCount()
        }
      } else if (type === 'ai' || type === 'aiTimeRang') {
        // updateAiReplyConfig
        Config[type] = data
        console.log("config::", Config);

        GlobalObject.viewManager.viewsMap.forEach(view => {
          view.webContents.send('updateAiReplyConfig', { ai: data, aiTimeRang: data })
        });

      }
    })
  }
  quitApp() {
    // 关闭应用--全屏应用
    ipcMain.handle('quitApp', (event, arg) => {
      if (Config.iscloseApp) {
        GlobalObject.mainWindow.hide() // 隐藏主窗口
      } else {
        app.quit()
      }
      return Config.iscloseApp
    })
  }
  hideApp() {
    // 关闭应用--全屏应用
    ipcMain.on('hideApp', () => {

      GlobalObject.mainWindow.hide() // 隐藏主窗口

    })
  }

}
