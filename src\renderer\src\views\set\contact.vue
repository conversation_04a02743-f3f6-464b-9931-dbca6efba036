<template>
	<div class="sys_base">
		<!-- 系统导航 -->
		<!-- <div :class="showHideWord?'sys_left':'sys_left sys_left_b'">
			<Tabar :softName='chatName' @changeChat='changeChat' @showHideWordFun='showHideWordFun'></Tabar>
		</div> -->
		<!-- 主要内容区 -->
		<div :class="tabarStore.showHideWord ? 'main_cont contact_main_cont' : 'main_cont contact_main_cont small_tab'">
			<!-- 联系我们 -->
			<div class="contact_cont">
				<div class="tit">
					<div class="tit_info">
						<h2>联系我们</h2>
						<span>Contact Us</span>
					</div>
				</div>
				<div class="conts">
					<div class="cont_info">
						<img :src="icon55" alt="">
						<div class="item">
							<img :src="icon56" alt="">
							<span>{{ siteInfoIndex.phone }}</span>
						</div>
						<div class="item">
							<img :src="icon57" alt="">
							<span>{{ siteInfoIndex.email }}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

</template>

<script setup>
import Tabar from '../../components/tabar.vue'
import {
	ref, watch,
	onMounted,
	onUnmounted
} from 'vue'
import {
	siteInfoSql
} from "../../api/account.js"
import { useTabStore } from "@renderer/store/index.js"
const tabarStore = useTabStore()
// import {
// 	getWorkOrderSql
// } from "../../api/account.js"
import { useRouter, useRoute } from 'vue-router'  // 导入 useRouter
const router = useRouter()  // 获取路由实例
const route = useRoute();  // 获取当前路由对象
// 导入本地图片
const icon55 = new URL('../../assets/img/icon55.svg', import.meta.url).href;
const icon56 = new URL('../../assets/img/icon56.svg', import.meta.url).href;
const icon57 = new URL('../../assets/img/icon57.svg', import.meta.url).href;

// 改变平台
const changeChat = (cN) => {
	chatName.value = cN
}
// 设置是否显示设置信息页面
const is_open = ref(false)
const openSetPage = (son_b) => {
	is_open.value = son_b
}
// 传递左侧栏目隐藏或展示文字方法
const showHideWord = ref(true)
watch(
	() => route.query,
	(newParams, oldParams) => {
		// 设置左侧是否显示
		if (newParams.chat == 'slide') {
			showHideWord.value = newParams.type == 'true' ? true : false
		}
	},
	{ immediate: true } // 立即执行一次，以确保初始参数也被捕获
);
// 首页获取站点信息
const siteInfoIndex = ref({})
function siteInfo() {
	siteInfoSql({}).then(response => {
		if (response.code == 1) {
			siteInfoIndex.value = response.data
		}
	})
}
siteInfo()
</script>

<style lang="scss">
@import url("../../assets/style/contact.scss");
</style>