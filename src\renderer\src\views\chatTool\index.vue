<template>
  <div class="sys_base">
    <div
      :class="
        tabarStore.showHideWord ? 'main_cont soft_main_cont' : 'main_cont soft_main_cont small_tab'
      "
    >
      <div
        ref="chatContent"
        :class="is_open ? 'webCont open_webCont' : 'webCont'"
        element-loading-text="Loading..."
        :element-loading-spinner="svg"
        element-loading-svg-view-box="-10, -10, 50, 50"
        element-loading-background="rgba(122, 122, 122, 0.8)"
      ></div>
      <!-- 软件打开设置 -->
      <div :class="is_open ? 'soft_set open_soft_set' : 'soft_set'">
        <UserSet
          :softName="chatName"
          ref="UserFun"
          @openSetPage="openSetPage"
          @translateSetParent="translateSetParent"
          @sendProloadJs="sendProloadJs"
        ></UserSet>
      </div>
    </div>
  </div>
</template>

<script setup>
import UserSet from '../../components/UserSet.vue'
import { ElMessage } from 'element-plus'
import {
  addMainAccount,
  addNewFans,
  getFansInfoSql,
  getOpenListSql,
  getLinkPrefix
} from '../../api/account.js'
import { translate } from '../../api/translate.js'

import { useRoute } from 'vue-router' // 导入 useRouter
import { useTabStore } from '@renderer/store/index.js'
const tabarStore = useTabStore()
const route = useRoute() // 获取当前路由对象
import { ref, watch, onMounted, inject, onUnmounted, useTemplateRef, onBeforeUnmount } from 'vue'

const linkPrefixList = ref({})
const UserFun = useTemplateRef('UserFun')
const getLinkPrefixList = async () => {
  const res = await getLinkPrefix({})
  if (res.code === 1) {
    linkPrefixList.value = res.data
  }
}
getLinkPrefixList()

import { useChatBounds } from '@renderer/utils/hooks.js'
const chatContent = ref()

useChatBounds({
  targeteElement: chatContent, callback: async (bounds) => {
    console.log(bounds);
  await window.electron.ipcRenderer.invoke('setViewBounds', bounds)
}})

onMounted(async () => {
  setFansRecord()
  // 显示备注信息和快捷回复
  UserFun.value.parent_top_son_show()
  onAddNewFans()
  onGetCurrentSeesionId()
  onGetPlatformUserInfo()
})
onUnmounted(() => {
  electron.ipcRenderer.removeAllListeners('sendActiveFansId')
  electron.ipcRenderer.removeAllListeners('onGetUserInfo')
  electron.ipcRenderer.removeAllListeners('onAddNewFansList')
})

const setFansRecord = async () => {
  if (UserFun.value) {
    UserFun.value.getFansInfo({})
    const i = await electron.ipcRenderer.invoke('getCurrentView')
    if (i && i.userId) {
      const { userId } = i;
      UserFun.value.getFansRecord(userId)
    }
  }
}

watch(
  () => route.query,
  (newParams) => {
    setFansRecord()
  }
)
// 是否打开用户设置 设置是否显示设置信息页面
const is_open = ref(false)
const openSetPage = (son_b) => {
  is_open.value = son_b
}
// 发送消息给渲染进程--预加载js
const sendProloadJs = (type, message, translateSet) => {
  // let translateSet = JSON.parse(localStorage.getItem('translateSet'))
  let formData = {
    text: message,
    engineCode: translateSet.engineCode,
    fromLang: 'auto',
    toLang: translateSet.send_lang
  }
  translate(formData).then((response) => {
    if (response.code == 1) {
      let tlMessage = response.data.result
      window.electron.ipcRenderer.invoke('sendMessageWindowToMain', { type, message: tlMessage })
    }
  })
}

const amount = ref('')
electron.ipcRenderer.on('noMoney', (_, data) => {
  if (amount.value == '') {
    amount.value = amount.value + '余额不足'
    ElMessage({
      showClose: true,
      message: data,
      type: 'warning'
    })
    setTimeout(function () {
      amount.value = ''
    }, 3000)
    UserFun.value.clearTransConfig('余额不足')
  }
})

const onGetPlatformUserInfo = () => {
  electron.ipcRenderer.on(
    'onGetUserInfo',
    (_, { nickName, phone, userId, platform, session_id, order_code }) => {
      let formData = {
        phoneNumber: phone,
        nickname: nickName,
        username: nickName,
        userId: userId,
        orderNumber: order_code,
        sessionId: session_id,
        link: `${linkPrefixList.value[platform === 'facebookBusiness' ? "facebookbusienss" : platform]}${phone}`
      }
      addMainAccount(formData).then((response) => {
        window.electron.ipcRenderer.send('updateOpenChatList')
        UserFun.value.getFansRecord(userId)
      })
    }
  )
}

const onGetCurrentSeesionId = () => {
  electron.ipcRenderer.on(
    'sendActiveFansId',
    (_, { platform, fansId, nickName, mainAccount, order_code }) => {
      let formData = {
        platformId: platform,
        mainAccount: mainAccount,
        fansAccount: fansId,
        orderNumber: order_code,
        fansUsername: nickName ? nickName : fansId
      }
      let fansInfoFormData = {
        mainAccount: mainAccount,
        fansAccount: fansId
      }

      getFansInfoSql(fansInfoFormData).then((response) => {
        if (response.code == 1 && response.data.id) {
          response.data.fans_level =
            response.data.fans_level > 0 ? String(response.data.fans_level) : ''
          response.data.order_stage =
            response.data.order_stage > 0 ? String(response.data.order_stage) : ''
          response.data.sales_stage =
            response.data.sales_stage > 0 ? String(response.data.sales_stage) : ''
          response.data.fans_sources =
            response.data.fans_sources > 0 ? String(response.data.fans_sources) : ''
          response.data.fans_name = response.data.fans_account_name

          if (UserFun.value) {
            let p = fansId.replace(/[\u202A-\u202E]/g, '')
            if (/^[0-9() +-]+$/g.test(p)) {
              response.data.fans_mobile
                ? response.data.fans_mobile
                : (response.data.fans_mobile = p)
            }
            UserFun.value.getFansInfo(response.data)
          }
        } else {
          UserFun.value.getFansInfo({})
        }

        if (response.code == 1 && response.data && !response.data.id) {
          if (UserFun.value) {
            UserFun.value.addCacheFansInfo(formData)
          }
        }
      })
      UserFun.value.getFansRecord(mainAccount)
      UserFun.value.getFansStatias(order_code)
    }
  )
}
const onAddNewFans = () => {
  electron.ipcRenderer.on(
    'onAddNewFansList',
    (_, { platform, unreadListInfo, mainAccount, order_code }) => {
      unreadListInfo.forEach((info) => {
        let formData = {
          platformId: platform,
          mainAccount: mainAccount,
          fansAccount: info.id,
          orderNumber: order_code,
          fansUsername: info.nickName ? info.nickName : info.id
        }
        addNewFans(formData).then((response) => {
          electron.ipcRenderer.send('updateOpenChatList')
        })
      })
    }
  )
}
</script>

<style lang="scss">
@import url('../../assets/style/soft.scss');
</style>
