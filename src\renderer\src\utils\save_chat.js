// import https  from 'https';
import http  from 'http';
import CryptoJ<PERSON> from "crypto-js";
import url  from 'url';
const urlStr = import.meta.env.VITE_BASE_API+'/api/chat/addChatRecord';

const urlObj = url.parse(urlStr);
export async function saveChat(data, token) {
	return new Promise((resolve, reject) => {
		const postData = JSON.stringify(data);
		const currentTime = new Date(); // 创建一个Date对象，表示当前时间
		const timestamp = Math.floor(currentTime.getTime() / 1000);
		const options = {
			hostname: urlObj.hostname,
			port: urlObj.port || 80,
			path: urlObj.pathname,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Content-Length': Buffer.byteLength(postData),
				'token':token,
				'timestamp':timestamp,
				'sign':sha256(postData+timestamp+token)
			},
		};

		const req = http.request(options, (res) => {
			let data = '';
			res.setEncoding('utf8');
			res.on('data', (chunk) => {
				data += chunk;
			});
			res.on('end', () => {
				try {
					const message = JSON.parse(data);
					resolve(message)
				} catch (error) {
					reject(error);
				}
			});
		});

		req.on('error', (error) => {
			reject(error);
		});

		req.write(postData);
		req.end();
	});
}

function sha256(message) {

   return CryptoJS.SHA256(message).toString();
}
