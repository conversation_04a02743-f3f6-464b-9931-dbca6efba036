<template>
  <div class="sys_base">
    <!-- 系统导航 -->
    <!-- <div :class="showHideWord?'sys_left':'sys_left sys_left_b'">
			<Tabar softName='/'></Tabar> 
		</div> -->
    <!-- 主要内容区 -->
    <div :class="tabarStore.showHideWord
  ? 'main_cont index_main_cont'
  : 'main_cont index_main_cont small_tab'
  ">
      <!-- webview 设置 -->
      <div class="index_content">
        <div class="top">
          <div class="left">
            <div class="wel_info">
              <div class="index_wel">
                <div class="logo">
                  <img :src="login_white" alt="" />
                  <div class="wel_tit">
                    <h2>{{ realname }} Hi, 欢迎来到蓝海译通!</h2>
                    <p>
                      当前版本：{{ app_version }}
                      <span v-if="
                        process.platform === 'darwin' && siteInfoIndex.version !== app_version
                      " style="display: inline-block; margin-left: 10px">当前最新版本：{{ siteInfoIndex.version }}</span>
                      <a v-if="
                        siteInfoIndex.version !== app_version
                      " style="text-decoration: none; color: white; display: inline-block"
                        href="https://www.blueglob.com/translator/" target="_blank">
                        <div class="income2">去下载</div>
                      </a>
                    </p>
                  </div>
                </div>
                <!-- <a style="text-decoration: none;" :href="'https://' + siteInfoIndex.website" target="_blank">
									<div class="income">充值</div>
								</a> -->
              </div>
              <div class="account_info">
                <div class="item">
                  <p>套餐类型：</p>
                  <span>{{ detailTc.set_meal_name }}</span>
                  <div class="flagshow">{{ detailTc.set_meal_name }}</div>
                </div>
                <div class="item">
                  <p title="在线/占用/分配端口数">在线/占用/分配端口数：</p>
                  <span>{{ detailTc.online_ports }}/{{ detailTc.used_ports }}/{{
                    detailTc.ports }}</span>
                  <div class="flagshow">
                    {{ detailTc.online_ports }}/{{ detailTc.used_ports }}/{{ detailTc.ports }}
                  </div>
                </div>
                <div class="item">
                  <p>开通日期：</p>
                  <span>{{ detailTc.start_time }}</span>
                  <div class="flagshow">{{ detailTc.start_time }}</div>
                </div>
                <div class="item">
                  <p>套餐有效期至：</p>
                  <span>{{ detailTc.expire_time }}</span>
                  <div class="flagshow">{{ detailTc.expire_time }}</div>
                </div>
                <div class="item">
                  <p>剩余字符：</p>
                  <span>{{ detailTc.remaining_traffic }}</span>
                  <div class="flagshow">{{ detailTc.remaining_traffic }}</div>
                </div>
                <div class="item">
                  <p>图片翻译：</p>
                  <span>{{ detailTc.picture_num }}</span>
                  <div class="flagshow">{{ detailTc.picture_num }}</div>
                </div>
              </div>
            </div>
            <ul class="tech_info">

              <li class="group item">
                <div
                  class="w-100% h-100% flex justify-center items-center relative group-hover:after:bg-blue-200/30 cursor-pointer after:content-[''] after:pointer-events-none after:absolute after:inset-0"
                  @click="toPath('/img_translate')">
                  <img :src="icon15" alt="" />
                  <div class="title">
                    <p>图片翻译</p>
                    <span>翻译图片内容</span>
                  </div>
                </div>
              </li>

              <li class="group item">
                <div
                  class="w-100% h-100% flex justify-center items-center relative group-hover:after:bg-blue-200/30 cursor-pointer after:content-[''] after:pointer-events-none after:absolute after:inset-0"
                  @click="toPath('/script')">
                  <img :src="icon16" alt="" />
                  <div class="title">
                    <p>推荐话术</p>
                    <span>实用话术</span>
                  </div>
                </div>
              </li>

              <li class="group item">
                <div
                  class="w-100% h-100% flex justify-center items-center relative group-hover:after:bg-blue-200/30 cursor-pointer after:content-[''] after:pointer-events-none after:absolute after:inset-0"
                  @click="toPath('/state')">
                  <img :src="icon17" alt="" />
                  <div class="title">
                    <p>计数器</p>
                    <span>数据统计</span>
                  </div>
                </div>
              </li>

              <li class="group item">
                <div
                  class="w-100% h-100% flex justify-center items-center relative group-hover:after:bg-blue-200/30 cursor-pointer after:content-[''] after:pointer-events-none after:absolute after:inset-0"
                  @click="open_ai_page">
                  <img :src="ai" alt="" />
                  <div class="title">
                    <p>AI助手</p>
                    <span>人工智能大模型</span>
                  </div>
                </div>
              </li>

            </ul>
          </div>
          <div class="right">
            <div class="right_cont">
              <div class="tast_tit">
                <div class="tit">
                  <h2>当前任务</h2>
                  <span>Current Tasks</span>
                </div>
                <div class="time">失效时间：{{ homeTask.expire_time }}</div>
              </div>
              <div class="tast_list">
                <h3>{{ homeTask.task_name }}</h3>
                <div class="tast_icon">
                  <div class="item" v-for="item in homeTask.work_order">
                    <img :src="item.icon" alt="" />
                  </div>
                </div>
              </div>
              <div class="remark">备注：{{ homeTask.task_description }}</div>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="left">
            <div class="left_top_tit">
              <div class="tit">
                <h2>增长曲线图</h2>
                <span>Growth curve chart</span>
              </div>
              <div class="manyDay">
                <div class="item">
                  <p>当日新增</p>
                  <span>{{ lineChart.day }}</span>
                </div>
                <div class="item">
                  <p>当周新增</p>
                  <span>{{ lineChart.week }}</span>
                </div>
                <div class="item">
                  <p>30天内新增</p>
                  <span>{{ lineChart.month }}</span>
                </div>
              </div>
              <div class="selPlant">
                <div class="img">
                  <img :src="icon60" alt="" />
                </div>
                <div class="select_div">
                  <el-select @change="changeSourceFun" v-model="plantState" placeholder="请选择社媒来源">
                    <el-option label="所有" value="0"></el-option>
                    <el-option label="telegram" value="1"></el-option>
                    <el-option label="whatsapp" value="2"></el-option>
                    <el-option label="facebook" value="3"></el-option>
                    <el-option label="instagram" value="4"></el-option>
                    <el-option label="zalo" value="5"></el-option>
                    <el-option label="twitter" value="6"></el-option>
                    <!-- <el-option label="facebookbusienss" value="7"></el-option> -->
                    <el-option label="tiktok" value="8"></el-option>
                    <el-option label="discord" value="9"></el-option>
                    <!-- <el-option v-for="item in workOrder" :label="item.platform" :value="item.platform"></el-option> -->
                  </el-select>
                </div>
              </div>
            </div>
            <div class="left_TT">
              <div class="leftT" ref="chartContainer"></div>
            </div>
          </div>
          <div class="right">
            <div class="plant_tit">
              <div class="tit">
                <h2>支持平台</h2>
                <span>自定义左侧显示的平台</span>
              </div>
              <div class="switch">
                <el-switch v-model="allOpen" disabled active-color="rgba(98, 109, 161, 1)"
                  inactive-color="rgba(98, 109, 161, 1)" inactive-text="一键全开">
                </el-switch>
              </div>
            </div>
            <!-- 平台列表 -->
            <div class="plant_list">
              <div class="item" v-for="item in openChatList">
                <div class="name">
                  <img :src="item.icon" alt="" />
                  <span style="display: inline-block; white-space: wrap;">{{ item.platform_name }}</span>
                </div>
                <el-switch v-model="item.status" :disabled="item.status == 2" @change="changePlantStatus"
                  :active-value="1" :inactive-value="0">
                </el-switch>
              </div>
              <div class="item" v-for="item in chatList">
                <div class="name">
                  <img :src="item.icon" alt="" />
                  <span>{{ item.name }}</span>
                </div>
                <el-switch v-model="item.is_open" :disabled="!item.is_use" :active-value="true" :inactive-value="false">
                </el-switch>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 软件打开设置 -->
      <!-- <div class="soft_set">
				<UserSet ref="UserSet"></UserSet>
			</div> -->
    </div>
  </div>
</template>

<script setup>
import Tabar from '../../components/tabar.vue'
import SoftSet from '../../components/SoftSet.vue'
import UserSet from '../../components/UserSet.vue'
import { useTabStore } from '@renderer/store/index.js'
const tabarStore = useTabStore()
import { ElMessage } from 'element-plus'
// 导入router
import { useRouter, useRoute } from 'vue-router' // 导入 useRouter
const router = useRouter() // 获取路由实例
const route = useRoute() // 获取当前路由对象
import * as echarts from 'echarts' // 导入Echarts 图表
import { ref, watch, onMounted, onUnmounted, reactive, onActivated } from 'vue'
import {
  getWorkOrderSql,
  getPortInfoSql,
  getTaskSql,
  getDetailSql,
  siteInfoSql,
  getAILink,
  getLineChartSql,
  getFansConfigSql,
  getPlatformSql,
  setPlatformSql
} from '../../api/account.js'
const process = reactive({
  ...window.electron.process
})
import { ElNotification } from 'element-plus'

// 导入本地图片
const login_white = new URL('../../assets/img/logo_white.png', import.meta.url).href
const icon15 = new URL('../../assets/img/icon15.svg', import.meta.url).href
const icon16 = new URL('../../assets/img/icon16.svg', import.meta.url).href
const icon17 = new URL('../../assets/img/icon17.svg', import.meta.url).href
const icon18 = new URL('../../assets/img/icon18.svg', import.meta.url).href
const icon19 = new URL('../../assets/img/icon19.svg', import.meta.url).href
const icon11 = new URL('../../assets/img/icon11.svg', import.meta.url).href
const icon12 = new URL('../../assets/img/icon12.svg', import.meta.url).href
const icon21 = new URL('../../assets/img/icon21.svg', import.meta.url).href
const icon22 = new URL('../../assets/img/icon22.svg', import.meta.url).href
const icon23 = new URL('../../assets/img/icon23.svg', import.meta.url).href
const icon24 = new URL('../../assets/img/icon24.svg', import.meta.url).href
const icon25 = new URL('../../assets/img/icon25.svg', import.meta.url).href
const icon26 = new URL('../../assets/img/icon26.svg', import.meta.url).href
const icon27 = new URL('../../assets/img/icon27.svg', import.meta.url).href
const icon28 = new URL('../../assets/img/icon28.svg', import.meta.url).href
const icon60 = new URL('../../assets/img/icon60.svg', import.meta.url).href
const ai = new URL('../../assets/img/ai.svg', import.meta.url).href
const realname = ref('')
if (localStorage.getItem('realname')) {
  realname.value = localStorage.getItem('realname')
}
// 首页获取站点信息
const siteInfoIndex = ref({})
async function siteInfo() {
  await siteInfoSql({}).then((response) => {
    if (response.code == 1) {
      siteInfoIndex.value = response.data
    }
  })
}

// 传递左侧栏目隐藏或展示文字方法
const showHideWord = ref(true)
watch(
  () => route.query,
  (newParams, oldParams) => {
    // 设置左侧是否显示
    if (newParams.chat == 'slide') {
      showHideWord.value = newParams.type == 'true' ? true : false
    }
  },
  { immediate: true } // 立即执行一次，以确保初始参数也被捕获
)
// 支持平台列表
const allOpen = ref(false)
const chatList = ref([
  // {
  // 	'name': 'WhatsApp',
  // 	'icon': icon11,
  // 	'is_open': true,
  // 	'is_use': false
  // },
  // {
  // 	'name': 'Telegram',
  // 	'icon': icon12,
  // 	'is_open': true,
  // 	'is_use': false
  // },
  // {
  // 	'name': 'Facebook',
  // 	'icon': icon23,
  // 	'is_open': true,
  // 	'is_use': false
  // }, {
  // 	'name': 'Instagram',
  // 	'icon': icon22,
  // 	'is_open': true,
  // 	'is_use': false
  // }, {
  // 	'name': 'Zalo',
  // 	'icon': icon26,
  // 	'is_open': true,
  // 	'is_use': false
  // },
  {
    name: 'whatsapp bussiness',
    icon: icon11,
    is_open: false,
    is_use: false
  },
  {
    name: 'Line',
    icon: icon21,
    is_open: false,
    is_use: false
  },
  {
    name: 'Telegram K',
    icon: icon12,
    is_open: false,
    is_use: false
  },
  {
    name: 'messenger',
    icon: icon24,
    is_open: false,
    is_use: false
  },
  {
    name: 'Tinder',
    icon: icon25,
    is_open: false,
    is_use: false
  },
  // {
  //   name: 'TikTok',
  //   icon: icon27,
  //   is_open: false,
  //   is_use: false
  // },
  {
    name: 'Snapchat',
    icon: icon28,
    is_open: false,
    is_use: false
  }
])
// const chatList = []
const openChatList = ref([])
const getPlatform = () => {
  getPlatformSql({}).then((response) => {
    if (response.code == 1) {
      openChatList.value = response.data.platform
    }
  })
}
getPlatform()
// 更改产品状态
const changePlantStatus = (e) => {
  setPlatformSql({ platform: openChatList.value }).then((response) => {
    getPlatform()
    window.electron.ipcRenderer.send('updateOpenChatList')
  })
}
const plantState = ref('0')
// 获取粉丝配置fansConfig
const fansConfig = ref({})

function getFansConfig() {
  getFansConfigSql({}).then((response) => {
    if (response.code == 1) {
      fansConfig.value = response.data
    }
  })
}
getFansConfig()

// 窗口编号
const windowNumber = ref(1)
// 初始化 ECharts 实例
const chartContainer = ref(null)
let chartInstance = null
const legend_selected = ref({
  telegram: true,
  whatsapp: true,
  facebook: true,
  instagram: true,
  zalo: true,
  twitter: true,
  facebookbusienss: true,
  tiktok: true,
  discord: true,
})

import { useWinResize } from 'vue-hooks-plus'

useWinResize(() => {
  setTimeout(() => {
    chartInstance.resize({
      width: "auto",
      height: "auto",
      animaiton: {
        duration: 300
      }
    })
  }, 500)
})

const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  chartInstance = echarts.init(chartContainer.value)
  const option = {
    color: ['#00C7F2', '#5577FF', '#8552a1', '#f391a9', '#f47920'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['telegram', 'whatsapp', 'facebook', 'instagram', 'zalo', "twitter", "tiktok", "discord"],
      // data: ['telegram', 'whatsapp', 'facebook', 'instagram', 'zalo', "twitter", "facebookbusienss", "tiktok", "discord"],
      left: 'left',
      selected: legend_selected.value,
      textStyle: {
        color: 'black'
      },
      show: true,
      tooltip: {
        show: true
      }
    },
    // toolbox: {
    // 	feature: {
    // 		saveAsImage: {}
    // 	}
    // },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: ['Jan', 'Fed', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: 'telegram',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(128, 255, 165)'
            },
            {
              offset: 1,
              color: 'rgb(1, 191, 236)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.telegram
      },
      {
        name: 'whatsapp',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: 'rgb(77, 119, 255)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.whatsapp
      },
      {
        name: 'facebook',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#8552a1'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.facebook
      },
      {
        name: 'instagram',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#f391a9'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.instagram
      },
      {
        name: 'zalo',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#f47920'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.zalo
      },
      {
        name: 'twitter',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#f47920'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.twitter

      },
      // {
      //   name: 'facebookbusienss',
      //   type: 'line',
      //   stack: 'Total',
      //   smooth: true,
      //   lineStyle: {
      //     width: 0
      //   },
      //   showSymbol: false,
      //   areaStyle: {
      //     opacity: 0.8,
      //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //       {
      //         offset: 0,
      //         color: 'rgb(0, 221, 255)'
      //       },
      //       {
      //         offset: 1,
      //         color: '#f47920'
      //       }
      //     ])
      //   },
      //   emphasis: {
      //     focus: 'series'
      //   },
      //   data: lineChart.value.facebookbusienss

      // },
      {
        name: 'tiktok',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 255)'
            },
            {
              offset: 1,
              color: '#f47920'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.tiktok

      },
      {
        name: 'discord',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 0
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgb(0, 221, 25)'
            },
            {
              offset: 1,
              color: '#f47920'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: lineChart.value.discord
      },
    ]
  }

  chartInstance.setOption(option)
}

// 首页 数据分析
const lineChart = ref({})
const getLineChart = () => {
  getLineChartSql({}).then((response) => {
    if (response.code == 1) {
      lineChart.value = response.data

      initChart()
    }
  })
}
// 改变来源方法 plantState.value
const changeSourceFun = () => {
  if (plantState.value == 0) {
    legend_selected.value = {
      telegram: true,
      whatsapp: true,
      facebook: true,
      instagram: true,
      zalo: true,
      twitter: true,
      facebookbusienss: true,
      tiktok: true,
      discord: true,
    }
  } else if (plantState.value == 1) {
    legend_selected.value = {
      telegram: true,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false
    }
  } else if (plantState.value == 2) {
    legend_selected.value = {
      telegram: false,
      whatsapp: true,
      facebook: false,
      instagram: false,
      zalo: false
    }
  } else if (plantState.value == 3) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: true,
      instagram: false,
      zalo: false
    }
  } else if (plantState.value == 4) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: true,
      zalo: false
    }
  } else if (plantState.value == 5) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: true
    }
  }
  else if (plantState.value == 6) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false,
      twitter: true,
      facebookbusienss: false,
      tiktok: false,
      discord: false,
    }
  }
  else if (plantState.value == 7) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false,
      twitter: false,
      facebookbusienss: true,
      tiktok: false,
      discord: false,


    }
  }
  else if (plantState.value == 8) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false,
      twitter: false,
      facebookbusienss: false,
      tiktok: true,
      discord: false,

    }
  }
  else if (plantState.value == 9) {
    legend_selected.value = {
      telegram: false,
      whatsapp: false,
      facebook: false,
      instagram: false,
      zalo: false,
      twitter: false,
      facebookbusienss: false,
      tiktok: false,
      discord: true,

    }
  }
  initChart()
}
// 获取聊天软件端口列表
const whtasappList = ref([])
const show_tap = ref({})
const getSoftList = (list, item) => {
  show_tap.value = item
  whtasappList.value = list
  open_dev()
}
// 获取在线/占用/分配端口数
const portInfo = ref({})
function getPortInfo() {
  getPortInfoSql({}).then((response) => {
    if (response.code == 1) {
      portInfo.value = response.data
    }
  })
}
getPortInfo()
// 传递token 给主进程
const sentMainToToken = () => {

  if (localStorage.getItem('token')) {
    let token = localStorage.getItem('token')
    let uuid = localStorage.getItem('uuid')
    electron.ipcRenderer.send('getUserLoginToken', { token, uuid })
  } else {
    setTimeout(function () {
      sentMainToToken()
    }, 1000)
  }
}
sentMainToToken()
// // 获取工单信息
const workOrder = ref({})

function getWorkOrder(orderNumber) {
  getWorkOrderSql({}).then((response) => {
    if (response.code == 1) {
      workOrder.value = response.data
    }
    localStorage.setItem('workOrder', JSON.stringify(response.data))
  })
}
getWorkOrder()
// // 获取首页任务信息
const homeTask = ref({})

function getTask() {
  getTaskSql({}).then((response) => {
    if (response.code == 1) {
      homeTask.value = response.data
    }
  })
}
getTask()
// 获取首页套餐详情
const detailTc = ref({})
function getDetail() {
  getDetailSql({}).then((response) => {
    if (response.code == 1) {
      detailTc.value = response.data
    }
  })
}
getDetail()

const toDownload = () => {
  window.open
}
// 监听窗口编号变化
watch(windowNumber, () => {
  initChart()
})

//监听版本数据
const app_version = ref('')

onMounted(async () => {
  window.electron.ipcRenderer.removeAllListeners('sendVersion')
  await siteInfo()
  // 监听版本数据
  window.electron.ipcRenderer.send('getVesion')
  window.electron.ipcRenderer.on('sendVersion', (event, data) => {
    app_version.value = data
    localStorage.setItem('version', data)
    if (
      window.___isShowVersionCount < 8 &&
      process.platform === 'darwin' &&
      siteInfoIndex.value.version !== app_version.value &&
      siteInfoIndex.value.version
    ) {
      ElNotification({
        title: '更新提示',
        message: `当前软件最新版本为${siteInfoIndex.value.version}，请及时更新`,
        type: 'warning',
        duration: 10000,
        showClose: true
      })
      window.___isShowVersionCount++
    }
  })
  // initChart();

  getLineChart()
})

onActivated(() => {
  getLineChart()
  getDetail()
})
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})

// 跳转页面
const toPath = (url) => {
  router.push({
    path: url
  })
}
const open_ai_page = async () => {
  const res = await getAILink({})
  if (localStorage.getItem('token')) {
    // let url = 'https://deepai.lhyq360.com/app/ai/oauth?code=' + localStorage.getItem('token')
    let url = res.data.link
    window.electron.ipcRenderer.send('open-new-window', url)
  } else {
    ElMessage({
      showClose: true,
      message: '请稍后重试',
      type: 'info'
    })
  }

  // router.push({
  // 	path:'/',
  // 	query:{'chat':'show_ai'}
  // })
  // setTimeout(function(){
  // 	router.push({
  // 		path:'/',
  // 		query:{'chat':'show_ai1'}
  // 	})
  // },100)
}
// 开发中
const creatingFun = () => {
  ElMessage({
    showClose: true,
    message: '开发中……',
    type: 'info'
  })
}
</script>

<style lang="scss">
@import url('../../assets/style/index.scss');
</style>
