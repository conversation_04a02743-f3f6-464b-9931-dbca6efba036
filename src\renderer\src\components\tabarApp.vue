<template>
  <div style="padding: 30px 10px;" :class="tabarStore.showHideWord ? 'sys_left' : 'sys_left sys_left_b'">
    <div class="sys_menu">
      <div v-show="tabarStore.showHideWord && tabarStore.isOpenAi">
        <div class="text-center text-green-500 text-xs  ml-0 mr-0 mb-5">
          AI客服已开启，正在智能回复中
        </div>
      </div>
      <div :class="tabarStore.nowTabbar == 'index' ? 'item on' : 'item'" @click="
        changeSySMenu('/', {
          nowTabbarId: 'index'
        })
        ">
        <div class="name">
          <img :src="icon10" alt="" />
          <span v-if="tabarStore.showHideWord">首页</span>
        </div>
      </div>
      <div style="overflow-y: auto; height: calc(100% - 160px);">
        <Draggable item-key="parentDraggable" animation="500" @start="onDragStart" @end="onDragEnd"
          v-if="Array.isArray(tabarStore.tabList)" v-model="tabarStore.tabList">
          <template #item="{ element: item }">
            <div :class="tabarStore.nowTabbar == item.platform ? 'item on' : 'item'">
              <div class="name !hover:bg-indigo-100" @click="
                changeSySMenu('/account', {
                  nowTabbarId: item.platform,
                  platform: item.platform
                })
                ">
                <img :src="item.icon" alt="" />
                <span v-if="tabarStore.showHideWord" :title="item.platform">{{ item.platform }}</span>
              </div>
              <div class="sonItems">
                <Draggable :item-key="() => item.id.toString()" animation="500" @start="onDragStart" @end="onDragEnd"
                  v-if="Array.isArray(item.lists)" v-model="item.lists">
                  <template #item="{ element: ite }">
                    <div style="display: flex; justify-content: space-between; align-items: center"
                      v-if="ite.status === 1"
                      :class="tabarStore.nowTabbar === `${ite.session_id}` ? 'name on' : 'name !hover:bg-indigo-100'"
                      @click="
                        changeSySMenu('/chat', {
                          nowTabbarId: ite.session_id,
                          platform: item.platform,
                          itemInfo: { ...ite }
                        })
                        ">
                      <div :class="tabarStore.showHideWord ? '' : 'w-1/2! items-center'"
                        style="width: calc(100% - 30px); display: flex; align-items: center">
                        <img :src="item.icon" alt="" />
                        <span :title="ite.account_name ? ite.account_name : ite.platform"
                          v-show="tabarStore.showHideWord">{{
                            ite.account_name ? ite.account_name : ite.platform }}</span>
                      </div>

                      <img v-show="tabarStore.showHideWord" style="width: 20px; height: 20px" :src="refresh"
                        alt="refresh" @click.stop="refreshView(`${ite.session_id}`)" />
                    </div>
                  </template>
                </Draggable>

              </div>
            </div>
          </template>
        </Draggable>
      </div>
    </div>
    <div class="show_hide cursor-pointer" @click="smallBigFun">
      <img v-show="tabarStore.showHideWord" :src="icon14" alt="" />
      <img v-show="!tabarStore.showHideWord" :src="rightSvg" alt="" />
      <span v-if="tabarStore.showHideWord">收起</span>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import refresh from '../assets/img/refresh.svg'
import rightSvg from '../assets/img/right.svg'
import Draggable from 'vuedraggable/src/vuedraggable';
// 定义和接收 props
const props = defineProps({
  softName: {
    type: String,
    required: true
  }
})
import { getOpenListSql } from '../api/account.js'
// 导入router
import { useRouter, useRoute } from 'vue-router' // 导入 useRouter
import { useTabStore } from '@renderer/store/index.js'
const tabarStore = useTabStore()
const router = useRouter() // 获取路由实例
const route = useRoute() // 获取当前路由对象
const list = ref([
  { id: 1, name: 'A' },
  { id: 2, name: 'B' },
])
watch(
  () => route.path,
  (newV, oldV) => {
    if (newV !== '/chat' && newV !== '/account') {
      tabarStore.nowTabbar = 'index'
    }
  }
)
// 导入图片
const icon10 = new URL('../assets/img/icon10.svg', import.meta.url).href
const icon11 = new URL('../assets/img/icon11.svg', import.meta.url).href
const icon12 = new URL('../assets/img/icon12.svg', import.meta.url).href
const icon13 = new URL('../assets/img/icon13.svg', import.meta.url).href
const icon14 = new URL('../assets/img/icon14.svg', import.meta.url).href
const baseUrl = ref(import.meta.env.VITE_BASE_API)
// 使用 afterEach 导航守卫
const nowPathName = ref(null)
// if(route.matched[0]){
// 	nowPathName.value = ref(route.matched[0].path)
// }
const mAccountId = ref()
const refreshView = (name) => {
  window.electron.ipcRenderer.send('refreshView', name)
}

const onDragStart = () => {
  console.log('onDragStart');

}
const onDragEnd = () => {
  console.log('onDragEnd');
}
onMounted(() => {
  changeSySMenu('/', {
    nowTabbarId: 'index'
  })
  // 监听是否更新会话列表
  window.electron.ipcRenderer.on('app-update-chat-list', (event) => {
    getOpenList()
  })
  nowPathName.value = route.path
  getOpenList()
})
// 获取账号列表
const getOpenList = () => {
  if (localStorage.getItem('token')) {
    getOpenListSql({}).then((response) => {
      if (response.code && response.code == 1) {
        tabarStore.tabList = response.data
        console.log(tabarStore.tabList, "+++++++++++")
      }
    })
  }
}

const platformMap = ref(
  new Map([
    ['facebook', 'https://www.facebook.com'],
    ['instagram', 'https://www.instagram.com/direct/t/lanhai/'],
    ['telegram', 'https://web.telegram.org/k'],
    ['whatsapp', 'https://web.whatsapp.com'],
    ['zalo', 'https://chat.zalo.me/'],
    ['tiktok', 'https://www.tiktok.com/messages'],
    ['facebookBusiness', 'https://business.facebook.com/latest/inbox/all/'],
    ['twitter', 'https://x.com/messages'],
    ['discord', 'https://discord.com/channels/@me'],
  ])
)

const changeSySMenu = (path, { nowTabbarId, platform, itemInfo }) => {
  if (nowTabbarId === tabarStore.nowTabbar && path !== '/account' && path === route.path) {
    return
  }
  let p = ""
  if (platform === "facebookbusienss") {
    p = "facebookBusiness"
  } else {
    p = platform
  }

  switch (path) {
    case '/':
      window.electron.ipcRenderer.send('showWindow')
      break
    case '/account':
      window.electron.ipcRenderer.send('showWindow')
      break
    case '/chat':
      window.electron.ipcRenderer.send('showView', {
        name: nowTabbarId,
        url: platformMap.value.get(p),
        platform: p,
        itemInfo
      })
      break
  }

  tabarStore.nowTabbar = nowTabbarId
  router.push({
    path,
    query: {
      nowTabbarId,
      platform,
    }
  })
}
// 收起侧边栏 放开侧边栏 true 是大的
const smallBigFun = () => {
  tabarStore.setShowHideWord(!tabarStore.$state.showHideWord)
}

// 暴露方法给父组件
defineExpose({
  getOpenList, // 获取账号列表
})
</script>

<style>
@import url('../assets/style/tabar.scss');
</style>
