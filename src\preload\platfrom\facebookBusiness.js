import { Platform } from '.'


export class FacebookBusinessHandler extends Platform {
  constructor(platform) {
    super(platform)
  }

  async getUserId() {
    if (!this.userId) {
      let u = await cookieStore.get('c_user')
      if (u) {
        this.userId = u.value
        let params = {
          userId: this.userId,
          platform: this.platform,
          phone: this.userId,
          nickName: this.userName || this.userId,
          session_id: this.viewSessionId
        }
        this.sendPlatformUserInfo(params)
      }
    }
  }
  userId
  chatUserId
  userName

  async init(translater) {
    await super.init(translater)
    this.getUserId()
    this.bindInputFunction()
  }
  _o(mutations, observer) {
    if (/\/latest\/inbox\//.test(location.pathname)) {
      // 绑定 input
      this.bindInputFunction()
      // 聊天页面
      this.translateList()
      // 获取未读消息的会话
      this.getUnreadSessionUserToFans()
      // 获取主账号信息
      this.getUserId()
      this.getCurrentSessionUserId()
    }
    this.getUserName()
  }

  _u(location) {
    this.getCurrentSessionUserId()
  }

  getUserName() {
    if (!this.userName) {
      let nameEl = document.querySelector(this.sI.userNameSelector)
      if (nameEl && this.userId) {
        if (!nameEl.innerText) {
          return
        }
        this.userName = nameEl.innerText
        
        let params = {
          userId: this.userId,
          platform: this.platform,
          phone: this.userId,
          nickName: this.userName || this.userId,
          session_id: this.viewSessionId
        }
        this.sendPlatformUserInfo(params)
      }
    }
  }

  getCurrentSessionUserId() {
    let count = 0
    const func = () => {
      let currentSessionUserNameEl = document.querySelector(
        this.sI.currentSessionUserNameElSelector
      )
      if (currentSessionUserNameEl && count < 5) {
        if (this.chatUserId !== currentSessionUserNameEl.innerText) {
          this.chatUserId = currentSessionUserNameEl.innerText
        } else {
          return
        }
        let params = {
          mainAccount: this.userId,
          fansId: this.chatUserId,
          nickName: this.chatUserId,
          platform: this.platform
        }
        this.sendCurrentSessionFansInfo(params)
      } else {
        setTimeout(() => {
          count++
          window.requestAnimationFrame(func)
        }, 500)
      }
    }
    window.requestAnimationFrame(func)
  }

  getUnreadSessionUserToFans() {
    const messageItemElList = document.querySelectorAll(this.sI.sessionElSelector)
    let unreadCount = 0
    const unreadListInfo = []
    messageItemElList.forEach((sEl) => {
      const unreadEl = sEl.querySelector(this.sI.unreadElSelector)
      if (unreadEl) {
        unreadCount += 1
        if (unreadEl.hasAttribute('aira-isread')) {
          return
        } else {
          unreadEl.setAttribute('aira-isread', 'true')
          let nickName = sEl.querySelector(this.sI.sessionUserNameElSelector)?.innerText
          unreadListInfo.push({
            id: nickName,
            nickName
          })
        }
      }
    })
    if (unreadCount > 0) {
      this.sendUnReadCount(unreadCount)
    }
    if (unreadListInfo.length > 0) {
      this.sendNewFansList({
        viewSessionId: this.viewSessionId,
        platform: this.platform,
        mainAccount: this.userId,
        unreadListInfo
      })
    }
  }
  bindInputFunction() {
    const func = (timestamp) => {
      const inputEL = document.querySelector(this.sI.inputElSelector)
      if (inputEL) {
        inputEL.addEventListener(
          'keydown',
          async (event) => {
            inputEL.setAttribute('aria-bind', 'true')
            if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
              console.log(event)
              event.stopPropagation()
              event.stopImmediatePropagation()
              this.changeInputEditStatus(false, this.sI.inputElSelector)
              let config = this.translater.config
              const isTranslate = config && config.trans_over
              const v = event.target.value
              if (isTranslate && v.trim()) {
                let tV = await this.translater.translateInput(v)
                console.log('tv', tV)

                tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, true)
              } else {
                await this.inputMessage(v, true)
              }

              this.switchShadowState(false)
            }
          },
          {
            capture: true
          },
          true
        )

        inputEL.addEventListener('input', (event) => {
          this.createMaskDiv()
        })
        this.createMaskDiv()
      } else {
        setTimeout(() => {
          window.requestAnimationFrame(func)
        }, 500)
      }
    }
    window.requestAnimationFrame(func)
  }

  sendMessageToInput({ type, message }) {
    this.inputMessage(message, type === 'send')
  }

  inputMessage(value, is_send = false) {
    this.changeInputEditStatus(true, this.sI.inputElSelector)
    return new Promise((resolve) => {
      let element = document.querySelector(this.sI.inputElSelector)
      if (element) {
        element.focus()
        element.setRangeText(value, 0, -1)
        element.dispatchEvent(
          new Event('input', {
            bubbles: true,
            data: value,
            inputType: 'insertText'
          })
        )
        if (is_send) {
          setTimeout(() => {
            document
              .querySelector(this.sI.sendButtonElSelector)
              ?.dispatchEvent(new MouseEvent('click', { bubbles: true, view: window }))
            resolve()
          }, 50)
        }
      }
    })
  }

  switchShadowState(flag) {
    let m = document.querySelector('#myShadow')
    if (!m) {
      return
    }

    if (flag) {
      m.style.display = 'block'
    } else {
      m.style.display = 'none'
    }
  }

  createMaskDiv() {
    const maskDiv = document.querySelector('#myShadow')
    if (!maskDiv) {
      // 创建遮罩---禁止点击按钮
      let shadwoDiv = document.createElement('div')
      shadwoDiv.id = 'myShadow'
      shadwoDiv.style.position = 'absolute'
      shadwoDiv.style.width = '68px'
      shadwoDiv.style.height = '40px'
      shadwoDiv.style.top = '0px'
      shadwoDiv.style.right = '0px'
      // shadwoDiv.style.background = 'red'
      shadwoDiv.style.zIndex = 9999
      let maskContainer = document.querySelector(this.sI.sendButtonContainerElSelector)
      if (maskContainer) {
        maskContainer.style.position = 'relative'
        maskContainer.appendChild(shadwoDiv)
      } else {
        return
      }

      shadwoDiv.addEventListener('click', async (event) => {
        event.stopPropagation()
        this.changeInputEditStatus(false, this.sI.inputElSelector)
        let config = this.translater.config
        const isTranslate = config && config.trans_over
        const inputEl = document.querySelector(this.sI.inputElSelector)

        const v = inputEl?.value
        console.log(v)
        if (isTranslate && v.trim()) {
          let tV = await this.translater.translateInput(v)
          tV ? await this.inputMessage(tV, true) : await this.inputMessage(v, true)
        } else {
          await this.inputMessage(v, true)
        }
        this.switchShadowState(false)
      })
    } else {
      this.switchShadowState(true)
    }
  }

  changeInputEditStatus(editStatus, selector) {
    try {
      let element = document.querySelector(selector)

      if (!element) return
      if (editStatus) {
        element.removeAttribute('disabled')
      } else {
        element.setAttribute('disabled', 'true')
      }
    } catch (error) {
      console.error(error)
    }
  }

  async translateList() {
    let listEL = document.querySelector(this.sI.messageListElSelector)
    if (listEL) {
      const reciveMessageElList = listEL.querySelectorAll(this.sI.reciveMessageElSelector)
      reciveMessageElList.forEach((el, index) => {
        this.translater.translateMessage(el, { type: 'in' })
      })

      const sendMessageElList = listEL.querySelectorAll(this.sI.sendMessageElSelector)
      sendMessageElList.forEach((el, index) => {
        this.translater.translateMessage(el, { type: 'out' })
      })
    }
  }
}
