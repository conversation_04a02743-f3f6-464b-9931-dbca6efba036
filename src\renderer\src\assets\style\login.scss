.login_page {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  .left_img {
    height: 100%;
    background: url('../img/loginbg.jpg') no-repeat center center;
    background-size: cover;
    width: 50%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    .imgs {
      width: 100%;
      height: 100%;
      padding-top: 20px;
      text-align: center;
      img {
        width: auto;
        max-height: 75%;
      }
      .info {
        width: 100%;
        font-family: Inter;
        font-size: 20px;
        font-weight: 400;
        line-height: 24.2px;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #fff;
        padding: 20px 0;
        a {
          color: #fff;
          text-decoration: none;
        }
      }
      .copy {
        width: 100%;
        font-family: Inter;
        font-size: 20px;
        font-weight: 400;
        line-height: 24.2px;
        text-align: center;
        padding-top: 20px;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #fff;
      }
    }
  }
  .login {
    width: 50%;
    height: 100%;
    overflow-y: auto;
    background-color: var(--bg-color-login);
    padding: 60px 60px 30px;
    .sys_name {
      display: flex;
      align-items: center;
      padding-bottom: 45px;
      padding-top: 20px;
      img {
        width: 41px;
        height: 41px;
        display: flex;
      }
      span {
        align-items: center;
        font-family: Inter;
        font-size: 37.33px;
        font-weight: 900;
        line-height: 45.18px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        padding-left: 20px;
        color: var(--font-color-login);
      }
    }
    .welcome_name {
      h2 {
        font-family: Nunito Sans;
        font-size: 53.33px;
        font-weight: 700;
        line-height: 53.33px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: var(--font-color-login);
        padding-bottom: 20px;
      }
      p {
        font-family: Nunito Sans;
        font-size: 21.33px;
        font-weight: 500;
        line-height: 32px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: rgba(143, 149, 178, 1);
      }
    }

    .formData {
      padding-top: 30px;
      .input_tit {
        font-family: Nunito Sans;
        font-size: 18.67px;
        font-weight: 400;
        line-height: 26.67px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: var(--font-color-login);
        padding: 20px 0 10px;
      }
      .input {
        display: flex;
        align-items: center;
        height: 70px;
        border: 1px solid rgba(216, 218, 229, 1);
        border-radius: 10px;
        padding: 0 2%;
        img {
          width: 32px;
          height: 32px;
          margin-right: 2%;
        }
        .el-input__wrapper {
          border: 0 !important;
          box-shadow: none;
          padding: 0;
          margin: 0;
          background-color: transparent;
          .el-input__inner {
            height: 68px;
            font-family: Nunito Sans;
            font-size: 21.33px;
            font-weight: 400;
            line-height: 32px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: var(--font-color-login);
          }
          .el-input__inner::placeholder {
            color: var(--font-color-login);
          }
        }
      }
      .remarks {
        display: flex;
        justify-content: space-between;
        font-family: Inter;
        font-size: 20px;
        font-weight: 500;
        line-height: 20px;
        text-align: left;
        padding: 30px 0;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        .agree {
          display: flex;
          align-items: center;
          color: var(--font-color-login);
        }
        a,
        span {
          color: rgba(85, 119, 255, 1);
          padding-left: 10px;
          text-decoration: none;
          cursor: pointer;
        }
      }
      .btn {
        width: 100%;
        background-color: rgba(32, 114, 247, 1);
        height: 60px;
        border-radius: 10px;
        font-family: Nunito Sans;
        font-size: 21.33px;
        font-weight: 700;
        line-height: 32px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
      }
      .forget {
        display: flex;
        align-items: center;
        font-family: Nunito Sans;
        font-size: 18.67px;
        font-weight: 400;
        line-height: 26.67px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: rgba(143, 149, 178, 1);
        a {
          color: rgba(85, 119, 255, 1);
        }
      }
    }
  }
  .agreement {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 9999;
    .cont {
      width: 960px;
      background-color: #fff;
      color: #000;
      height: calc(100% - 80px);
      margin: 40px auto 0;
      box-shadow: 0 0 6px #ccc;
      border-radius: 10px;
      padding: 30px;
      overflow: hidden;
      position: relative;
      h2 {
        text-align: center;
        padding-bottom: 10px;
        height: 50px;
        position: relative;
        .close {
          position: absolute;
          top: -15px;
          right: -15px;
        }
      }
      .info {
        height: calc(100% - 60px);
        overflow-y: auto;
      }
    }
  }
}
