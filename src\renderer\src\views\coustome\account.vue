<template>
	<div class="sys_base">
		<!-- 主要内容区 -->
		<div :class="tabarStore.showHideWord ? 'main_cont account_main_cont' : 'main_cont account_main_cont small_tab'">
			<!-- 会话列表 -->
			<div :class="is_open ? 'account_cont open_account_cont' : 'account_cont'">
				<div class="chatTit">
					<div class="tit">
						<h2>会话列表</h2>
						<span>占用/总数: {{ portInfo.used_ports }}/{{ portInfo.ports }}</span>
					</div>
					<div class="btns">
						<!-- <div class="item">
							<img :src="icon37" alt="">
							<p>分享链接</p>
						</div> -->
						<div class="item" @click="onAddSoft">
							<img :src="icon38" alt="">
							<p>新增会话</p>
						</div>
						<div class="items">
							<div class="item" @click="openCharFun()">一键启动</div>
							<!-- <div class="item">一键关闭</div>
							<div class="item">群发</div> -->
							<div class="item" @click="delChat">批量删除</div>
						</div>
					</div>
					<div class="clock">
						<span>已链接安全加密链路</span>
						<img :src="icon36" alt="">
					</div>
				</div>
				<!-- 会话列表 -->
				<div class="chatList">
					<el-table empty-text="无数据" :data="tableData" tooltip-effect="dark" height="100%" style="width: 100%;"
						@selection-change="handleSelectionChange">
						<template #empty>
							<div style="padding:40px 0 30px"><img :src="empty" alt="" /></div>
						</template>
						<el-table-column type="selection" width="55">
						</el-table-column>

						<!-- <el-table-column
						  prop="id"
						  label="序号"
						  width="60">
						</el-table-column> -->
						<el-table-column label="序号" type="index" width="60" />
						<el-table-column prop="create_time" label="创建时间" width="160">
						</el-table-column>
						<el-table-column prop="platform" label="会话记录" width="100">
						</el-table-column>
						<el-table-column prop="account_name" label="昵称" width="120">
						</el-table-column>
						<el-table-column prop="account_code" label="用户名" width="120">
						</el-table-column>
						<el-table-column prop="account_mobile" label="手机号" width="120">
						</el-table-column>
						<el-table-column prop="name" label="状态" width="100">
							<template #default="scope">
								<el-text v-if="scope.row.status == 1" class="mx-1" type="primary">开启</el-text>
								<el-text v-if="scope.row.status == 2" class="mx-1" type="danger">关闭</el-text>
							</template>
						</el-table-column>
						<el-table-column prop="address" label="地区" show-overflow-tooltip>
						</el-table-column>
						<!-- <el-table-column
						  prop="create_time"
						  label="最后修改"
						  width="160">
						</el-table-column> -->
						<el-table-column prop="name" label="备注信息" width="180">
						</el-table-column>
						<el-table-column prop="name" label="操作" width="150" fixed="right">
							<template #default="scope">
								<el-button v-if="scope.row.status == 2" class="setBtn" @click="updateStatus([scope.row.session_id], 1)"
									type="text" size="small">启动</el-button>
								<el-button v-if="scope.row.status == 1" class="setBtn close"
									@click="updateStatus([scope.row.session_id], 2)" type="text" size="small">关闭</el-button>
								<el-button class="setBtn del" @click="delOneChat(scope.row)" type="text" size="small">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<!-- 分页设置 -->
				<!-- <div class="pageInfo">
					<el-pagination
					  background
					  :page-size='15'
					  layout="prev, pager, next"
					  :total="1000">
					</el-pagination>
				</div> -->
			</div>
			<!-- 用户设置 -->
			<div :class="is_open ? 'soft_set open_soft_set' : 'soft_set'">
				<UserSet ref="UserSetRef" @openSetPage='openSetPage' :softName='chatName'></UserSet>
			</div>
		</div>
	</div>

</template>

<script setup>
import Tabar from '../../components/tabar.vue'
// import SoftSet from '../../components/SoftSet.vue'
import UserSet from '../../components/UserSet.vue'
import {
	ref,
	watch,
	onMounted,
	toRaw
} from 'vue'
import {
	v4 as uuidv4
} from 'uuid';
import {
	getFansInfoSql,
	isNumberOver,
	addChatInfo,
	delChatAccount,
	getListSql,
	getPortInfoSql,
	updateStatusSql
} from "../../api/account.js"
import {
	ElMessage,
	ElMessageBox
} from 'element-plus'
import {
	useRouter,
	useRoute
} from 'vue-router' // 导入 useRouter
import { useTabStore } from '@renderer/store/index.js'
const tabarStore = useTabStore()
const router = useRouter() // 获取路由实例
const route = useRoute(); // 获取当前路由对象
// 导入本地图片
const icon15 = new URL('../../assets/img/icon15.svg', import.meta.url).href;
const icon16 = new URL('../../assets/img/icon16.svg', import.meta.url).href;
const icon17 = new URL('../../assets/img/icon17.svg', import.meta.url).href;
const icon36 = new URL('../../assets/img/icon36.svg', import.meta.url).href;
const icon37 = new URL('../../assets/img/icon37.svg', import.meta.url).href;
const icon38 = new URL('../../assets/img/icon38.svg', import.meta.url).href;
const empty = new URL('../../assets/img/icon52.svg', import.meta.url).href;

// 支持平台列表
const chatName = ref()
onMounted(() => {
	chatName.value = route.query.platform
	getchatList()
})
// 传递左侧栏目隐藏或展示文字方法
const showHideWord = ref(true)
watch(
	() => route.query,
	(newParams, oldParams) => {
		// 当路由参数变化时执行逻辑
		// console.log(' account Route params changed:', newParams);
		// 你也可以访问旧的参数，如果需要的话
		// console.log('account Old route params:', oldParams);
		//  newParams.platform == 'facebookbusienss' || newParams.platform == 'tiktok' || newParams.platform == 'whatsapp' || newParams.platform == 'telegram' || newParams.platform == 'facebook' || newParams.platform == 'instagram' || newParams.platform == 'zalo'
		if (newParams.platform) {
			chatName.value = newParams.platform
			getchatList()
		}
		// 设置左侧是否显示
		if (newParams.chat == 'slide') {
			showHideWord.value = newParams.type == 'true' ? true : false
		}
		// 添加编辑删除会话
	}
);
// 更改会话状态等信息
const updateChatInfo = () => {
	// console.log('==== accountlist')
	router.push({
		path: '/account',
		query: {
			'chat': 'updateChat',
			'type': Math.random()
		}
	})
}
// 改变平台
const changeChat = (cN) => {
	chatName.value = cN
	// 重新查询会话
	getchatList()
}
// 设置是否显示设置信息页面
const is_open = ref(false)
const openSetPage = (son_b) => {
	is_open.value = son_b
}
// 添加软件列表
const onAddSoft = async () => {
	try {
		await ElMessageBox.confirm("是否新建会话端口?", {
			title: '提示',
			confirmButtonText: "确认",
			cancelButtonText: "取消"
		})
		// 判断是否达到增加端口上线
		// 判断会话是否达到上限
		isNumberOver({}).then(response => {
			if (response.code == 1 && response.data.status == 0) {
				ElMessage({
					showClose: true,
					message: '会话已达上限',
					type: 'warn'
				})
			} else {
				let src;
				switch (chatName.value) {
					case "telegram":
						src = "https://web.telegram.org/k";
						break;
					case "whatsapp":
						src = "https://web.whatsapp.com/";
						break;
					case "facebook":
						src = "https://www.facebook.com/";
						break;
					case "instagram":
						src = "https://www.instagram.com/direct/t/";
						break;
					case "zalo":
						src = "https://chat.zalo.me/";
						break;
					default:
						src = "";
				}

				const key = `${uuidv4()}${parseInt(new Date().getTime() / 1000)}`;
				const partition = "persist:" + `${uuidv4()}${parseInt(new Date().getTime() / 1000)}`;
				const map = {
					user_id: "",
					nickname: chatName.value,
					username: "",
					phone: "",
					key,
					partition,
					url: src,
					idlist: [],
					state: false, // 是否打开会话
					unread: 0,
				};
				// 添加会话
				createChat(partition)

			}
		})
	} catch (error) {

	}

}
// 获取在线/占用/分配端口数
const portInfo = ref({})

function getPortInfo() {
	getPortInfoSql({}).then(response => {
		if (response.code == 1) {
			portInfo.value = response.data
		}
	})
}
getPortInfo()
// 一键启动回话
const openCharFun = async () => {
	let chatList = selChatList.value;
	let sessionid_arr = []
	for (let i = 0; i < chatList.length; i++) {
		sessionid_arr.push(chatList[i].session_id)
	}
	let formData = {
		"sessionIds": sessionid_arr
	}
	await updateStatus(sessionid_arr, 1)


	let batchStartViewList = []
	const platformMap = ref(
		new Map([
			['facebook', 'https://www.facebook.com'],
			['instagram', 'https://www.instagram.com/direct/t/lanhai/'],
			['telegram', 'https://web.telegram.org/k'],
			['whatsapp', 'https://web.whatsapp.com'],
			['zalo', 'https://chat.zalo.me/'],
			['tiktok', 'https://www.tiktok.com/messages'],
			['facebookBusiness', 'https://business.facebook.com/latest/inbox/all/'],
			['twitter', 'https://x.com/messages'],
			['discord', 'https://discord.com/channels/@me'],
		])
	)
	chatList.forEach(item => {
		let p = ""
		if (item.platform === "facebookbusienss") {
			p = "facebookBusiness"
		} else {
			p = item.platform
		}

		batchStartViewList.push({
			name: item.session_id,
			url: platformMap.value.get(p),
			platform: p,
			itemInfo: toRaw(item),
			noAttach: true,
		})
	})
	window.electron.ipcRenderer.send('batchStartView', { batchStartViewList })
}
// updateStatusSql 更改回话状态
const updateStatus = async (item, status) => {
	if (status == 2) {
		window.electron.ipcRenderer.send('deleteView', item.map(i => `${i.platform}/${i.id}`).join(","))
	}

	return updateStatusSql({
		'sessionId': item,
		'status': status
	}).then(response => {
		if (response.code == 1) {
			ElMessage({
				showClose: true,
				message: response.msg,
				type: 'success'
			})
			// 更新会话列表
			getchatList()
			window.electron.ipcRenderer.send('updateOpenChatList')
		} else {
			ElMessage({
				showClose: true,
				message: response.msg,
				type: 'info'
			})

		}
	})
}
// 创建会话
const createChat = (sessionId) => {
	let formData = {
		"platformId": chatName.value,
		"sessionId": sessionId
	}

	addChatInfo(formData).then(response => {
		if (response.code == 1) {
			ElMessage({
				showClose: true,
				message: '创建会话成功',
				type: 'success'
			})
			window.electron.ipcRenderer.send('updateOpenChatList')
			// 更新会话列表
			getchatList()
		} else {
			ElMessage({
				showClose: true,
				message: response.msg,
				type: 'info'
			})

		}
	})
}
// 表格数据
const tableData = ref([])
// 获取会话列表
const getchatList = () => {
	let formData = {
		"platformId": chatName.value,
	}
	getListSql(formData).then(res => {
		if (res.code == 1) {
			tableData.value = res.data
		} else {
			tableData.value = []
		}
		// 更新tabar数据
		// updateChatInfo()
	})

}

// 删除会话
const delChat = () => {
	let chatList = selChatList.value;


	let sessionid_arr = []
	for (let i = 0; i < chatList.length; i++) {
		sessionid_arr.push(chatList[i].session_id)
	}
	let formData = {
		"sessionIds": sessionid_arr
	}
	if (sessionid_arr.length == 0) {
		ElMessage({
			showClose: true,
			message: '请选择要删除的账号',
			type: 'info'
		})
		return;
	}
	ElMessageBox.confirm(
		'确认要删除当前账号吗?',
		'删除提醒', {
		distinguishCancelAndClose: true,
		confirmButtonText: '确定',
		cancelButtonText: '取消',
	}
	)
		.then(() => {
			delChatAccount(formData).then(response => {
				if (response.code == 1) {
					window.electron.ipcRenderer.send('deleteView', chatList.map(item => `${item.platform}/${item.id}`).join(","))
					// 删除本地存储对话
					ElMessage({
						showClose: true,
						message: '删除会话成功',
						type: 'success'
					})
					// 重新获取会话列表
					window.electron.ipcRenderer.send('updateOpenChatList')
					getchatList()
				} else {
					ElMessage({
						showClose: true,
						message: response.msg,
						type: 'info'
					})

				}
			})
		})
		.catch(() => {

		})


}
// 删除单个会话
const delOneChat = (val) => {
	selChatList.value = [val]
	console.log(val);

	delChat()
}
// 获取选中会话信息
const selChatList = ref([])
const handleSelectionChange = (val) => {
	selChatList.value = val
}
// 开启会话  --- 暂时删除用法
// const openChatList = (type='all',param='') =>{
// 	let paramData = ''
// 	if(type=='all'){
// 		if(selChatList.value.length > 0){
// 			paramData = JSON.stringify(selChatList.value)
// 		}else{
// 			ElMessage({
// 			    showClose: true,
// 			    message: '请先选择要打开的会话',
// 			    type: 'info'
// 			})
// 		}
// 	}else{
// 		paramData = JSON.stringify([param])
// 	}
// 	let url= ''
// 	if(chatName.value=='whatsapp'){
// 		url = '/whatsapp'
// 	}else if(chatName.value=='telegram'){
// 		url = '/telegram'
// 	}
// 	if(url != '' && paramData !=''){
// 		router.push({
// 			path:url,
// 			query:{'param':paramData}
// 		})
// 	}
// }
// const handleClick = (item)=>{
// 	openChatList('one',item)
// }
</script>

<style lang="scss">
@import url("../../assets/style/account.scss");

.el-table__empty-block {
	width: auto !important;
	height: auto !important;
}
</style>