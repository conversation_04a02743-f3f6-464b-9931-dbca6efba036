<svg width="76" height="64" viewBox="0 0 76 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_2144_2035)">
<circle cx="38" cy="30" r="22" fill="#4B4A4A"/>
<g clip-path="url(#clip0_2144_2035)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M39.2665 20.45C38.8975 19.1975 37.1245 19.1975 36.7555 20.45L36.6145 20.9285C36.4959 21.3304 36.2889 21.7007 36.0087 22.0123C35.7284 22.3238 35.3821 22.5687 34.9949 22.7291C34.6078 22.8894 34.1896 22.9612 33.7712 22.939C33.3527 22.9168 32.9445 22.8013 32.5765 22.601L32.1385 22.361C30.9925 21.7385 29.7385 22.991 30.3625 24.1385L30.601 24.5765C30.8013 24.9445 30.9168 25.3527 30.939 25.7712C30.9612 26.1896 30.8894 26.6078 30.7291 26.9949C30.5687 27.3821 30.3238 27.7284 30.0123 28.0087C29.7007 28.2889 29.3304 28.4959 28.9285 28.6145L28.45 28.7555C27.1975 29.1245 27.1975 30.8975 28.45 31.2665L28.9285 31.4075C29.3306 31.526 29.7011 31.733 30.0128 32.0134C30.3245 32.2937 30.5696 32.6402 30.7299 33.0276C30.8903 33.4149 30.962 33.8333 30.9397 34.2519C30.9173 34.6705 30.8016 35.0789 30.601 35.447L30.361 35.8835C29.7385 37.0295 30.991 38.2835 32.1385 37.6595L32.5765 37.421C32.9446 37.2206 33.3529 37.1052 33.7715 37.0831C34.19 37.061 34.6082 37.1329 34.9953 37.2934C35.3825 37.4539 35.7289 37.699 36.009 38.0107C36.2892 38.3225 36.4961 38.6929 36.6145 39.095L36.7555 39.572C37.1245 40.8245 38.8975 40.8245 39.2665 39.572L39.4075 39.0935C39.526 38.6913 39.733 38.3208 40.0134 38.0091C40.2937 37.6974 40.6402 37.4524 41.0276 37.292C41.4149 37.1316 41.8333 37.06 42.2519 37.0823C42.6705 37.1046 43.0789 37.2203 43.447 37.421L43.8835 37.661C45.0295 38.2835 46.2835 37.031 45.6595 35.8835L45.421 35.447C45.2203 35.0788 45.1047 34.6703 45.0825 34.2516C45.0602 33.8329 45.132 33.4145 45.2925 33.0271C45.4531 32.6398 45.6983 32.2933 46.0102 32.013C46.322 31.7327 46.6927 31.5258 47.095 31.4075L47.572 31.2665C48.8245 30.8975 48.8245 29.1245 47.572 28.7555L47.0935 28.6145C46.6916 28.4959 46.3213 28.2889 46.0097 28.0087C45.6981 27.7284 45.4532 27.3821 45.2928 26.9949C45.1325 26.6078 45.0608 26.1896 45.0829 25.7712C45.1051 25.3527 45.2206 24.9445 45.421 24.5765L45.661 24.1385C46.2835 22.9925 45.031 21.7385 43.8835 22.3625L43.447 22.601C43.0788 22.8019 42.6703 22.9178 42.2515 22.9403C41.8327 22.9628 41.4142 22.8912 41.0266 22.7308C40.6391 22.5704 40.2924 22.3253 40.0119 22.0134C39.7315 21.7015 39.5244 21.3308 39.406 20.9285L39.265 20.45H39.2665ZM35.317 20.0255C36.1075 17.3405 39.9145 17.3405 40.705 20.0255L40.846 20.504C40.9012 20.6914 40.9976 20.8641 41.1282 21.0094C41.2589 21.1547 41.4203 21.269 41.6009 21.3437C41.7814 21.4185 41.9763 21.452 42.1714 21.4416C42.3666 21.4313 42.5569 21.3774 42.7285 21.284L43.1665 21.044C45.6265 19.706 48.3175 22.3955 46.9765 24.8555L46.738 25.2935C46.6445 25.4651 46.5906 25.6554 46.5803 25.8505C46.57 26.0456 46.6034 26.2406 46.6782 26.4211C46.753 26.6016 46.8672 26.7631 47.0125 26.8937C47.1578 27.0243 47.3305 27.1208 47.518 27.176L47.9965 27.317C50.6815 28.1075 50.6815 31.9145 47.9965 32.705L47.518 32.846C47.3305 32.9012 47.1578 32.9976 47.0125 33.1282C46.8672 33.2589 46.753 33.4203 46.6782 33.6009C46.6034 33.7814 46.57 33.9763 46.5803 34.1714C46.5906 34.3666 46.6445 34.5569 46.738 34.7285L46.978 35.1665C48.3175 37.6265 45.625 40.3175 43.1665 38.9765L42.7285 38.738C42.5569 38.6445 42.3666 38.5906 42.1714 38.5803C41.9763 38.57 41.7814 38.6034 41.6009 38.6782C41.4203 38.753 41.2589 38.8672 41.1282 39.0125C40.9976 39.1578 40.9012 39.3305 40.846 39.518L40.705 39.9965C39.9145 42.6815 36.1075 42.6815 35.317 39.9965L35.176 39.518C35.1208 39.3305 35.0243 39.1578 34.8937 39.0125C34.7631 38.8672 34.6016 38.753 34.4211 38.6782C34.2406 38.6034 34.0456 38.57 33.8505 38.5803C33.6554 38.5906 33.4651 38.6445 33.2935 38.738L32.8555 38.978C30.3955 40.3175 27.706 37.625 29.0455 35.1665L29.284 34.7285C29.3774 34.5569 29.4313 34.3666 29.4416 34.1714C29.452 33.9763 29.4185 33.7814 29.3437 33.6009C29.269 33.4203 29.1547 33.2589 29.0094 33.1282C28.8641 32.9976 28.6914 32.9012 28.504 32.846L28.0255 32.705C25.3405 31.9145 25.3405 28.1075 28.0255 27.317L28.504 27.176C28.6914 27.1208 28.8641 27.0243 29.0094 26.8937C29.1547 26.7631 29.269 26.6016 29.3437 26.4211C29.4185 26.2406 29.452 26.0456 29.4416 25.8505C29.4313 25.6554 29.3774 25.4651 29.284 25.2935L29.044 24.8555C27.706 22.3955 30.397 19.706 32.8555 21.0455L33.2935 21.284C33.4651 21.3774 33.6554 21.4313 33.8505 21.4416C34.0456 21.452 34.2406 21.4185 34.4211 21.3437C34.6016 21.269 34.7631 21.1547 34.8937 21.0094C35.0243 20.8641 35.1208 20.6914 35.176 20.504L35.317 20.0255Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.9999 26.6309C37.1063 26.6309 36.2494 26.9858 35.6176 27.6176C34.9858 28.2494 34.6309 29.1063 34.6309 29.9999C34.6309 30.8934 34.9858 31.7503 35.6176 32.3821C36.2494 33.0139 37.1063 33.3689 37.9999 33.3689C38.8934 33.3689 39.7503 33.0139 40.3821 32.3821C41.0139 31.7503 41.3689 30.8934 41.3689 29.9999C41.3689 29.1063 41.0139 28.2494 40.3821 27.6176C39.7503 26.9858 38.8934 26.6309 37.9999 26.6309ZM33.1309 29.9999C33.1309 29.3605 33.2568 28.7273 33.5015 28.1366C33.7462 27.5458 34.1048 27.0091 34.557 26.557C35.0091 26.1048 35.5458 25.7462 36.1366 25.5015C36.7273 25.2568 37.3605 25.1309 37.9999 25.1309C38.6393 25.1309 39.2724 25.2568 39.8631 25.5015C40.4539 25.7462 40.9906 26.1048 41.4428 26.557C41.8949 27.0091 42.2535 27.5458 42.4982 28.1366C42.7429 28.7273 42.8689 29.3605 42.8689 29.9999C42.8689 31.2912 42.3559 32.5296 41.4428 33.4428C40.5296 34.3559 39.2912 34.8689 37.9999 34.8689C36.7085 34.8689 35.4701 34.3559 34.557 33.4428C33.6438 32.5296 33.1309 31.2912 33.1309 29.9999Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_2144_2035" x="0" y="0" width="76" height="76" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.560784 0 0 0 0 0.584314 0 0 0 0 0.698039 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2144_2035"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2144_2035" result="shape"/>
</filter>
<clipPath id="clip0_2144_2035">
<rect width="24" height="24" fill="white" transform="translate(26 18)"/>
</clipPath>
</defs>
</svg>
