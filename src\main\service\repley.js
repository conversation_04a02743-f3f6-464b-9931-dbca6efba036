import axios from 'axios';

const API_BASE_URL = 'https://aikf.lhyq360.com/app/ai';

let RolePrompt;
export const getDatasetRolePrompt = async (params) => {
  var config = {
    method: 'post',
    url: 'http://aikf.lhyq360.com/app/ai/embedding/getDateset',
    headers: {
      'Content-Type': 'application/json',
      'Accept': '*/*',
      'Host': 'aikf.lhyq360.com',
      'Connection': 'keep-alive'
    },
    data: JSON.stringify(params)
  };
  const res = await axios(config)
  RolePrompt = res.data?.data?.rolePrompt
  return res.data?.data?.rolePrompt
}



/**
 * 根据关键词查询system数据
 * @param {string} content - 提问的内容
 * @param {number} dataset - 数据集ID，默认为1
 * @returns {Promise<Array>} 返回查询到的数据数组
 */
async function searchSystemData({ content, dataset = 1 }) {
  try {
    const { data: response } = await axios.post(`${API_BASE_URL}/embedding/search`, {
      content,
      dataset
    });

    if (response.data && response.code === 0) {
      return response || {};
    } else {
      console.log(response, content, dataset);
      
      console.log(new Error(response.msg || '查询system数据失败'));
    }
  } catch (error) {
    console.error('搜索system数据时发生错误:', error);
    throw error;
  }
}

/**
 * 聊天回复接口
 * @param {Object} params - 聊天参数
 * @returns {Promise<Object>} 返回聊天回复结果
 */
async function chatCompletions(params) {
  console.log("params", params);
  // 示例请求参数结构（注释形式）
  /*
  const exampleParams = {
    "temperature": 0.5,
    "stream": false,
    "messages": [
      {
        "role": "system",
        "content": "你作为隆德风机公司的专属客服，根据客户聊天的语言，回复对应的语言，回复需要简单，直接。如有风机类产品需求时，请给他列举我们的三条产品 带上URL链接，如果无法得到答案或者提到价格问题，请让对方留下联系方式  以及邮箱"
      },
      {
        "role": "assistant",
        "content": "好风机，隆德造， 真诚沟通，用心服务"
      },
      {
        "role": "user",
        "content": "风机哪个公司做的好\n"
      }
    ],
    "prompt": "风机哪个公司做的好\n", // 用户提问的内容
    "model": "gpt-4o-mini", // 固定
    "chat_id": 1751439533036, // 当天聊天室的ID 可使用用户的ID
    "user_message_id": "1751439558807763", // 微妙时间戳
    "assistant_message_id": "1751439558807691", // 微妙时间戳
    "role_id": 41, // 先固定 41  模拟使用 后续根据请求的 商户 需要请求 获取 此ID
    "max_tokens": 2000 // 固定
  };
  */

  try {
    const response = await axios.post(`${API_BASE_URL}/chat/completions`, params);

    if (response.data) {
      return response.data;
    }

  } catch (error) {
    console.error('聊天回复时发生错误:', error);

  }
}

const createNowMicroseconds = () => {
  const ns = process.hrtime.bigint() // 纳秒时间戳（1微秒 = 1000纳秒）
  const us = ns / 1000n

  return Number(us)
}



export const getAiReplyApi = async ({ question, dataset, messageList, chat_id }) => {
  console.log("getAiReplyApi", question, dataset, messageList, chat_id);
  
  const { code, data, msg } = await searchSystemData({ content: question, dataset })

  if (code === 0) {
    return await chatCompletions({
      "temperature": 0.5,
      "stream": false,
      "messages": [
        {
          "role": "system",
          "content": `${RolePrompt}${data.toString()}`
          // "content": data.toString()
        },
        ...messageList.map((v) => {
          return {
            "role": v.role,
            "content": v.content
          }
        })
      ],
      "prompt": question, // 用户提问的内容
      "model": "gpt-4o-mini", // 固定
      "chat_id": chat_id, // 当天聊天室的ID 可使用用户的ID
      "user_message_id": createNowMicroseconds(), // 微妙时间戳
      "assistant_message_id": createNowMicroseconds(), // 微妙时间戳
      "role_id": dataset, // 先固定 41  模拟使用 后续根据请求的 商户 需要请求 获取 此ID
      "max_tokens": 2000 // 固定
    })
  } else {
    console.error(msg)
  }
}

// 默认导出
export default { searchSystemData, chatCompletions };