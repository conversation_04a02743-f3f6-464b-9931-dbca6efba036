<template>
	<div class="soft_list">
		<div class="item">
			<img :src="icon10" alt="">
			<span>首页</span>
		</div>
		<!-- <div v-for="(item,index) in list" @click="changeSoftTab(item)" :class="selState==item.id?'item on':'item'">
			{{item.nickname}}
			<div class="desl" @click="delChat(item.key)"> 删除账号</div>
		</div> -->
		<div class="item">
			<img :src="icon11" alt="">
			<span>WhatsApp</span>
		</div>
		<div class="item">
			<img :src="icon12" alt="">
			<span>Telegram</span>
		</div>
	</div>
	<div class="add_btn" @click="onAddSoft">
		<img :src="icon13" alt="">
		<span>添加端口</span>
	</div>
</template>

<script setup>
import {
	ref,
	onMounted,
	onUnmounted,
} from 'vue'
import { v4 as uuidv4 } from 'uuid';
// 定义和接收 props
const props = defineProps({
	softName: {
		type: String,
		required: true,
	},
});
import { ElMessage } from 'element-plus'
import { getFansInfoSql, isNumberOver, addChatInfo, delChatAccount, setTranslateConfigrSql } from "../api/account.js"

// 设置默认的whtasapp账号列表
const list = ref([])
// 这只选中whatsapp 列表状态
const selState = ref(null)
const emit = defineEmits(['getSoftList']);
const changeSoftTab = (item) => {
	selState.value = item.id
	emit('getSoftList', list.value, item);
	// this.$emit("getSoftList", list.value);
}
// 添加软件列表
const onAddSoft = () => {
	// 判断是否达到增加端口上线
	// 判断会话是否达到上限
	isNumberOver({}).then(response => {
		if (response.code == 1 && response.data.status == 0) {
			ElMessage({
				showClose: true,
				message: '会话已达上限',
				type: 'success'
			})
		} else {
			let src;
			switch (props.softName) {
				case "telegram":
					src = "https://web.telegram.org/k";
					break;
				case "whatsapp":
					src = "https://web.whatsapp.com/";
					break;
				default:
					src = "";
			}

			const key = `${uuidv4()}${parseInt(new Date().getTime() / 1000)}`;
			const partition = "persist:" + `${uuidv4()}${parseInt(new Date().getTime() / 1000)}`;
			const map = {
				user_id: "",
				nickname: props.softName,
				username: "",
				phone: "",
				key,
				partition,
				url: src,
				idlist: [],
				state: false, // 是否打开会话
				unread: 0,
				get_set: {
					form: {
						engine: "baidu",
						from_: "auto",
						to_: "zh",
					},
					type: "get",
					switch: false,
				},
				send_set: {
					form: {
						engine: "baidu",
						from_: "auto",
						to_: "zh",
					},
					type: "send",
					switch: false,
				},
			};
			if (list.value.length == 0) {
				list.value = [map]
			} else {
				list.value = [...list.value, map];
			}
			localStorage.setItem(props.softName + 'softList', JSON.stringify(list.value));
			createChat(key)
		}
	})


}

// 创建会话
const createChat = (sessionId) => {
	let formData = {
		"platformId": props.softName,
		"sessionId": sessionId
	}
	addChatInfo(formData).then(response => {
		if (response.code == 1) {
			ElMessage({
				showClose: true,
				message: '创建会话成功',
				type: 'success'
			})
		} else {
			ElMessage({
				showClose: true,
				message: response.msg,
				type: 'warn'
			})

		}
	})
}
// 删除会话
const delChat = (sessionId) => {
	let formData = {
		"sessionIds": [sessionId]
	}
	delChatAccount(formData).then(response => {
		if (response.code == 1) {
			// 删除本地存储对话
			delLocalStorage(sessionId)
			ElMessage({
				showClose: true,
				message: '删除会话成功',
				type: 'success'
			})
		} else {
			ElMessage({
				showClose: true,
				message: response.msg,
				type: 'warn'
			})

		}
	})
}
// 更新本地存储 
function updateLocalStorage(selItem) {
	let chatList = list.value
	for (let i = 0; i < chatList.length; i++) {
		if (chatList[i].key == selItem.key) {
			chatList[i] = selItem
		}
	}
	list.value = chatList
	localStorage.setItem(props.softName + 'softList', JSON.stringify(list.value));
}
// 删除本地储存里面的数据
function delLocalStorage(key) {
	let chatList = list.value // 当前对话列表
	let newChatList = []      // 新对话列表
	for (let i = 0; i < chatList.length; i++) {
		if (chatList[i].key != key) {
			newChatList.push(chatList[i])
		}
	}
	list.value = newChatList
	localStorage.setItem(props.softName + 'softList', JSON.stringify(list.value));
}
// 组件挂在完成======
onMounted(() => {
	list.value = JSON.parse(localStorage.getItem(props.softName + 'softList')) ? JSON.parse(localStorage.getItem(props.softName + 'softList')) : [];
})
// 暴露方法给父组件
defineExpose({
	updateLocalStorage, // 更新本地婚车
});
</script>

<style lang="scss">
@import url('/src/assets/style/soft_set.scss');
</style>