:root {
	--ev-c-white: #ffffff;
	--ev-c-white-soft: #f8f8f8;
	--ev-c-white-mute: #f2f2f2;

	--ev-c-black: #1b1b1f;
	--ev-c-black-soft: #222222;
	--ev-c-black-mute: #282828;

	--ev-c-gray-1: #515c67;
	--ev-c-gray-2: #414853;
	--ev-c-gray-3: #32363f;

	--ev-c-text-1: rgba(255, 255, 245, 0.86);
	--ev-c-text-2: rgba(235, 235, 245, 0.6);
	--ev-c-text-3: rgba(235, 235, 245, 0.38);

	--ev-button-alt-border: transparent;
	--ev-button-alt-text: var(--ev-c-text-1);
	--ev-button-alt-bg: var(--ev-c-gray-3);
	--ev-button-alt-hover-border: transparent;
	--ev-button-alt-hover-text: var(--ev-c-text-1);
	--ev-button-alt-hover-bg: var(--ev-c-gray-2);
}

:root {
	--color-background: var(--ev-c-black);
	--color-background-soft: var(--ev-c-black-soft);
	--color-background-mute: var(--ev-c-black-mute);

	--color-text: var(--ev-c-text-1);
}

*,
*::before,
*::after {
	box-sizing: border-box;
	margin: 0;
	font-weight: normal;
}


::-webkit-scrollbar {
	width: 0;
	height: 0;
}


ul {
	list-style: none;
}

body {
	min-height: 100vh;
	color: var(--color-text);
	line-height: 1.6;
	font-family:
		Inter,
		-apple-system,
		BlinkMacSystemFont,
		'Segoe UI',
		Roboto,
		Oxygen,
		Ubuntu,
		Cantarell,
		'Fira Sans',
		'Droid Sans',
		'Helvetica Neue',
		sans-serif;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

:root {
	--bg-color-linear: linear-gradient(90deg, #D1E2FC 0%, #E6F5FF 66.96%, #FAFBFF 100%);
	--bg-color-login: #fff;
	--font-color-login: rgba(8, 23, 53, 1);
	--bg-color-cont: #fff;
	--bg-color-left-menu: #f6f6f6;
	--font-color-left-menu-select: rgba(85, 119, 255, 1);
	--bg-color-left-menu-select: rgba(222, 230, 255, 1);

	--bg-color-index-echars: #f1f1f1;
	--font-color-index-echars: #606266;

	--bg-color-index-task: linear-gradient(135deg, #F1F8FE 40%, #C1D7EF 100%);
	--font-color-index-task-time: rgba(143, 149, 178, 1);
	--font-color-index-remark: rgba(98, 109, 161, 1);

	--border-color-translate: #DDDFE7;

	--font-color-contact: #2072F7;
}

html[data-theme="dark"] {
	--bg-color-linear: #000;
	--bg-color-login: #000;
	--font-color-login: #fff;
	--bg-color-cont: rgba(53, 53, 53, 1);
	--bg-color-left-menu: rgba(53, 53, 53, 1);
	--font-color-left-menu-select: #fff;
	--bg-color-left-menu-select: rgba(88, 130, 255, 1);

	--bg-color-index-echars: #4E4E4E;
	--font-color-index-echars: #fff;

	--bg-color-index-task: linear-gradient(135deg, rgb(96, 98, 103) 40%, rgb(41, 58, 84) 100%);
	--font-color-index-task-time: #fff;
	--font-color-index-remark: #fff;

	--border-color-translate: #626DA1;

	--font-color-contact: #fff;
}

body {
	width: 100vw;
	height: 100vh;
	background: var(--bg-color-linear);
}

.translate {
	width: 100vw;
	height: 100vh;
}
ol {
	list-style: decimal;
}

/* 主架构 */
.sys_base {
	width: 100vw;
	height: calc(100vh - 70px);
	/* height: 100vh; */
	margin-top: 70px;
}

.sys_base .sys_left {
	width: 192px;
	height: calc(100vh - 90px);
	left: 10px;
	top: 80px;
	border-radius: 10px;
	position: fixed;
	background-color: var(--bg-color-cont);
	/* transition: all 0.3s; */
	display: none;
}

.sys_base .main_cont {
	display: flex;
	justify-content: space-between;
	height: calc(100vh - 70px);
	width: calc(100% - 222px);
	margin-left: 212px;
	padding: 10px 0;
	transition: all 0.3s;
}


/* 账号信息 */
.systemBarLogin,
.systemBar {
	position: fixed;
	width: 100vw;
	top: 0;
	left: 0;
	height: 60px;
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.systemBar {
	height: 70px;
}

.systemBarLogin .sys_deal,
.systemBar .sys_deal {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	width: 160px;
	z-index: 10000;
}

.systemBarLogin .sys_deal .out,
.systemBar .sys_deal .out {
	margin-right: 15px;
}

.systemBarLogin .out:last-child,
.systemBar .out:last-child {
	margin-right: 25px;
}


.systemBarLogin .login_in .comp_btn,
.systemBarLogin .login_in .user_btn {
	display: none;
}








.systemBar .login_in {
	width: calc(100% - 160px);
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.systemBar .login_in .comp_btn {
	width: 835px;
	display: flex;
	align-items: center;
}

.systemBar .login_in .comp_btn .sys_name {
	display: flex;
	align-items: center;
	width: 214px;
	padding-left: 25px;
}

.systemBar .login_in .comp_btn .sys_name img {
	width: 41px;
	height: 41px;
}

.systemBar .login_in .comp_btn .sys_name span {
	font-family: Inter;
	font-size: 28px;
	font-weight: 900;
	line-height: 33.89px;
	text-align: left;
	padding-left: 20px;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: var(--font-color-login);
}

.systemBar .login_in .comp_btn .web_url {
	background-color: var(--bg-color-cont);
	display: flex;
	align-items: center;
	line-height: 41px;
	height: 41px;
	width: 218px;
	border-radius: 10px;
	padding: 0 15px;
	margin: 0 25px;
	box-shadow: 0px 8px 16px 0px rgba(143, 149, 178, 0.15);

}

.systemBar .login_in .comp_btn .web_url a {
	color: var(--font-color-login);
	text-decoration: none;
}

.systemBar .login_in .comp_btn .web_url img {
	width: 24px;
	height: 24px;
}

.systemBar .login_in .comp_btn .web_url span {
	font-family: Nunito Sans;
	font-size: 14px;
	font-weight: 400;
	line-height: 19.1px;
	text-align: left;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: var(--font-color-login);
	padding-left: 10px;
	display: block;
	width: calc(100% - 24px);
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.systemBar .login_in .comp_btn .message {
	background-color: var(--bg-color-cont);
	display: flex;
	align-items: center;
	line-height: 41px;
	height: 41px;
	width: 350px;
	border-radius: 10px;
	padding: 0 15px;
	box-shadow: 0px 8px 16px 0px rgba(143, 149, 178, 0.15);
}

.systemBar .login_in .comp_btn.hide_message .message {
	display: none;
}

.systemBar .login_in .comp_btn .message img {
	width: 24px;
	height: 24px;
}

.systemBar .login_in .comp_btn .message span {
	font-family: Nunito Sans;
	font-size: 14px;
	font-weight: 400;
	line-height: 19.1px;
	text-align: left;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: rgba(253, 113, 175, 1);
	padding-left: 10px;
	display: block;
	width: 50px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.systemBar .login_in .comp_btn .message .scroll_text {
	width: calc(100% - 75px);
	display: flex;
	flex-wrap: nowrap;
	overflow: hidden;
	height: 41px;
	position: relative;
	align-items: center;
}

.systemBar .login_in .comp_btn .message .scroll_text p {
	font-family: Nunito Sans;
	font-size: 14px;
	font-weight: 400;
	line-height: 19.1px;
	white-space: nowrap;
	text-align: left;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: rgba(253, 113, 175, 1);
	position: absolute;
	left: 0;
}

.systemBar .user_btn {
	width: calc(100% - 850px);
	max-width: 1000px;
	display: flex;
	justify-content: flex-end;

}

.systemBar .user_btn .items {
	display: flex;
	align-items: center;
	margin-left: 15px;
	position: relative;
}

.systemBar .user_btn .items img {
	width: 76px;
	height: 64px;
}

.systemBar .user_btn .items span {
	font-family: Nunito Sans;
	font-size: 17px;
	font-weight: 400;
	line-height: 24.55px;
	width: 108px;
	text-align: left;
	text-underline-position: from-font;
	text-decoration-skip-ink: none;
	color: var(--font-color-login)
}

/* 隐藏文字 */
.systemBar .user_btn.span_hide .items span {
	display: none;
	position: absolute;
	bottom: -16px;
	left: 4px;
}

.systemBar .user_btn.span_hide .items:first-child span {
	left: -16px;
}

.systemBar .user_btn.span_hide .items:hover span {
	display: block;
}

.shadow {
	position: fixed;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.3);
	top: 0;
	left: 0;
	z-index: 9999;
}