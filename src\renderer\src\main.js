import 'virtual:uno.css'
import '@unocss/reset/normalize.css'
import '@unocss/reset/tailwind-compat.css'
import './assets/style/main.css'

// 配置WASM加载
import wasm from "../public/wasm/dotlottie-player.wasm?url"
import { setWasmUrl } from '@lottiefiles/dotlottie-vue'
import { initializeWasm, setupCSPForWasm, isElectronEnvironment } from './utils/wasmLoader.js'

// 异步初始化WASM
async function initWasmSupport() {
  try {
    // 如果是 Electron 环境，设置 CSP
    if (isElectronEnvironment()) {
      setupCSPForWasm()
    }

    // 初始化 WASM
    const wasmInitialized = await initializeWasm(wasm)
    if (wasmInitialized) {
      setWasmUrl(wasm)
      console.log('WASM initialized successfully:', wasm)
    } else {
      console.warn('WASM initialization failed, Lottie animations may not work properly')
    }
  } catch (error) {
    console.error('Failed to initialize WASM support:', error)
  }
}

// 立即初始化 WASM 支持
initWasmSupport()

// 添加简单的测试函数到全局
window.testWasm = async function () {
  console.log('🧪 开始测试 WASM 功能...')

  try {
    // 测试基本 WebAssembly 支持
    if (typeof WebAssembly === 'undefined') {
      throw new Error('WebAssembly 不支持')
    }

    // 创建简单的 WASM 模块测试
    const wasmCode = new Uint8Array([
      0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00,
      0x01, 0x07, 0x01, 0x60, 0x02, 0x7f, 0x7f, 0x01, 0x7f,
      0x03, 0x02, 0x01, 0x00,
      0x07, 0x07, 0x01, 0x03, 0x61, 0x64, 0x64, 0x00, 0x00,
      0x0a, 0x09, 0x01, 0x07, 0x00, 0x20, 0x00, 0x20, 0x01, 0x6a, 0x0b
    ])

    const wasmModule = await WebAssembly.instantiate(wasmCode)
    const result = wasmModule.instance.exports.add(5, 3)

    if (result === 8) {
      console.log('✅ WebAssembly 基本功能正常')
    } else {
      throw new Error(`计算错误: 期望 8, 得到 ${result}`)
    }

    // 测试 Lottie WASM 文件
    const wasmUrl = new URL('../public/wasm/dotlottie-player.wasm', import.meta.url).href
    const response = await fetch(wasmUrl)
    if (response.ok) {
      const wasmBytes = await response.arrayBuffer()
      console.log('✅ Lottie WASM 文件加载成功，大小:', wasmBytes.byteLength, 'bytes')
    } else {
      throw new Error('Lottie WASM 文件加载失败')
    }

    console.log('🎉 所有 WASM 测试通过！')
    return true
  } catch (error) {
    console.error('❌ WASM 测试失败:', error)
    return false
  }
}
import { createApp } from 'vue'
import App from './App.vue'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
// 持久化插件
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
import router from './router'
import i18n from './utils/i118'
import { zhCn } from 'element-plus/es/locale/index.mjs'
const app = createApp(App)
app.use(pinia)
app.use(i18n)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})
app.mount('#app')

// // 开发环境下添加 WASM 测试功能
// if (import.meta.env.DEV) {
//   import('./utils/wasmTest.js').then(({ addTestButton, runFullWasmTest }) => {
//     // 页面加载完成后添加测试按钮
//     setTimeout(() => {
//       addTestButton()
//       // 自动运行一次测试
//       runFullWasmTest().then(results => {
//         console.log('自动 WASM 测试完成:', results)
//       })
//     }, 2000)
//   })
// }

electron.ipcRenderer.invoke('getPlatform').then((res) => {
  window.platform = res
})
