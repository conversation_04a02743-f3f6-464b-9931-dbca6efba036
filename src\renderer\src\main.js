import 'virtual:uno.css'
import '@unocss/reset/normalize.css'
import '@unocss/reset/tailwind-compat.css'
import './assets/style/main.css'

// 配置WASM加载
import wasm from "../public/wasm/dotlottie-player.wasm?url"
import { setWasmUrl } from '@lottiefiles/dotlottie-vue'
import { initializeWasm, setupCSPForWasm, isElectronEnvironment } from './utils/wasmLoader.js'

// 异步初始化WASM
async function initWasmSupport() {
  try {
    // 如果是 Electron 环境，设置 CSP
    if (isElectronEnvironment()) {
      setupCSPForWasm()
    }

    // 初始化 WASM
    const wasmInitialized = await initializeWasm(wasm)
    if (wasmInitialized) {
      setWasmUrl(wasm)
      console.log('WASM initialized successfully:', wasm)
    } else {
      console.warn('WASM initialization failed, Lottie animations may not work properly')
    }
  } catch (error) {
    console.error('Failed to initialize WASM support:', error)
  }
}

// 立即初始化 WASM 支持
initWasmSupport()

// 添加详细的 CSP 层级测试函数
window.testWasm = async function () {
  console.log('🧪 开始测试 WASM 功能和 CSP 配置...')

  // 检查当前的 CSP 配置
  console.log('📋 当前 CSP 配置检查:')

  // 1. 检查 HTML meta 标签
  const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]')
  if (metaCSP) {
    console.log('✅ HTML meta CSP 存在:', metaCSP.getAttribute('content'))
  } else {
    console.log('❌ HTML meta CSP 不存在')
  }

  // 2. 检查环境信息
  console.log('🌍 环境信息:')
  console.log('- Electron 环境:', typeof window.process !== 'undefined')
  console.log('- WebAssembly 支持:', typeof WebAssembly !== 'undefined')
  console.log('- User Agent:', navigator.userAgent)

  try {
    // 3. 测试基本 WebAssembly 支持
    console.log('\n🔬 测试 1: 基本 WebAssembly 功能')
    if (typeof WebAssembly === 'undefined') {
      throw new Error('WebAssembly 不支持')
    }

    // 创建简单的 WASM 模块测试
    const wasmCode = new Uint8Array([
      0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00,
      0x01, 0x07, 0x01, 0x60, 0x02, 0x7f, 0x7f, 0x01, 0x7f,
      0x03, 0x02, 0x01, 0x00,
      0x07, 0x07, 0x01, 0x03, 0x61, 0x64, 0x64, 0x00, 0x00,
      0x0a, 0x09, 0x01, 0x07, 0x00, 0x20, 0x00, 0x20, 0x01, 0x6a, 0x0b
    ])

    const wasmModule = await WebAssembly.instantiate(wasmCode)
    const result = wasmModule.instance.exports.add(5, 3)

    if (result === 8) {
      console.log('✅ WebAssembly 基本功能正常')
    } else {
      throw new Error(`计算错误: 期望 8, 得到 ${result}`)
    }

    // 4. 测试 Lottie WASM 文件
    console.log('\n🔬 测试 2: Lottie WASM 文件加载')
    const wasmUrl = new URL('../public/wasm/dotlottie-player.wasm', import.meta.url).href
    console.log('WASM 文件 URL:', wasmUrl)

    const response = await fetch(wasmUrl)
    if (response.ok) {
      const wasmBytes = await response.arrayBuffer()
      console.log('✅ Lottie WASM 文件加载成功，大小:', wasmBytes.byteLength, 'bytes')

      // 验证 WASM 文件格式
      const isValid = WebAssembly.validate(wasmBytes)
      console.log('✅ WASM 文件格式验证:', isValid ? '有效' : '无效')
    } else {
      throw new Error(`Lottie WASM 文件加载失败: HTTP ${response.status}`)
    }

    console.log('\n🎉 结论: 当前配置下所有 WASM 测试通过！')
    console.log('📝 这意味着只使用 HTML meta 标签的 CSP 配置就足够了')
    return true
  } catch (error) {
    console.error('\n❌ WASM 测试失败:', error)
    console.log('📝 这意味着需要额外的 CSP 配置层级')
    return false
  }
}
import { createApp } from 'vue'
import App from './App.vue'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
// 持久化插件
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
import router from './router'
import i18n from './utils/i118'
import { zhCn } from 'element-plus/es/locale/index.mjs'
const app = createApp(App)
app.use(pinia)
app.use(i18n)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})
app.mount('#app')

// // 开发环境下添加 WASM 测试功能
// if (import.meta.env.DEV) {
//   import('./utils/wasmTest.js').then(({ addTestButton, runFullWasmTest }) => {
//     // 页面加载完成后添加测试按钮
//     setTimeout(() => {
//       addTestButton()
//       // 自动运行一次测试
//       runFullWasmTest().then(results => {
//         console.log('自动 WASM 测试完成:', results)
//       })
//     }, 2000)
//   })
// }

electron.ipcRenderer.invoke('getPlatform').then((res) => {
  window.platform = res
})
