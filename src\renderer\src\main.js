import 'virtual:uno.css'
import '@unocss/reset/normalize.css'
import '@unocss/reset/tailwind-compat.css'
import './assets/style/main.css'

// 配置WASM加载
import wasm from "../public/wasm/dotlottie-player.wasm?url"
import { setWasmUrl } from '@lottiefiles/dotlottie-vue'
import { initializeWasm, setupCSPForWasm, isElectronEnvironment } from './utils/wasmLoader.js'

// 异步初始化WASM
async function initWasmSupport() {
  try {
    // 如果是 Electron 环境，设置 CSP
    if (isElectronEnvironment()) {
      setupCSPForWasm()
    }

    // 初始化 WASM
    const wasmInitialized = await initializeWasm(wasm)
    if (wasmInitialized) {

      console.log('WASM initialized successfully:', wasm)
    } else {
      console.warn('WASM initialization failed, Lottie animations may not work properly')
    }
  } catch (error) {
    console.error('Failed to initialize WASM support:', error)
  }
}

// 立即初始化 WASM 支持
initWasmSupport()
import { createApp } from 'vue'
import App from './App.vue'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
// 持久化插件
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
import router from './router'
import i18n from './utils/i118'
import { zhCn } from 'element-plus/es/locale/index.mjs'
const app = createApp(App)
app.use(pinia)
app.use(i18n)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})
app.mount('#app')

// // 开发环境下添加 WASM 测试功能
// if (import.meta.env.DEV) {
//   import('./utils/wasmTest.js').then(({ addTestButton, runFullWasmTest }) => {
//     // 页面加载完成后添加测试按钮
//     setTimeout(() => {
//       addTestButton()
//       // 自动运行一次测试
//       runFullWasmTest().then(results => {
//         console.log('自动 WASM 测试完成:', results)
//       })
//     }, 2000)
//   })
// }

electron.ipcRenderer.invoke('getPlatform').then((res) => {
  window.platform = res
})
