import { defineStore } from 'pinia'

import { ref } from 'vue'
export const useTabStore = defineStore(
  'tabarStore',
  () => {
    const tabList = ref([])
    const nowTabbar = ref("index")
    const showHideWord = ref(true)
    const setShowHideWord = (value) => {
      showHideWord.value = value
    }

    const isOpenAi = ref("")
    return {
      tabList,
      showHideWord,
      setShowHideWord,
      nowTabbar,
      isOpenAi
    }
  },
  {
    persist: true
  }
)