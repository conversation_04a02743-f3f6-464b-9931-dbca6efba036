<template>
	<div class="sys_menu">
		<!-- <div class="item">
			<div to="/whatsapp">whatsapp</div>
		</div>
		<div class="item">
			<div to="/telegram">telegram</div>
		</div>
		<div class="item" @click="clearLocal">清空所有信息</div>
		
		<div class="item">
			<div to="/login">登录</div>
		</div> -->
		<div :class="props.softName == '/' ? 'item on' : 'item'" @click="changeSySMenu('index', '/', 'top')">
			<div class="name">
				<img :src="icon10" alt="">
				<span v-if="sideBigSmall">首页</span>
			</div>
		</div>
		<div v-for="item in openList" :class="props.softName == item.platform ? 'item on' : 'item'">
			<div class="name" @click="changeSySMenu(item.platform, '/account', 'top')">
				<img :src="item.icon" alt="">
				<span v-if="sideBigSmall">{{ item.platform }}</span>
			</div>
			<div class="sonItems">
				<div v-for="ite in item.lists" :class="ite.id == mAccountId ? 'name on' : 'name'"
					@click="changeSySMenu(ite.id, '/' + ite.platform, ite.platform)">
					<img :src="item.icon" alt="">
					<span v-if="sideBigSmall">{{ ite.account_name ? ite.account_name : ite.platform }}</span>
				</div>
			</div>
		</div>
		<!-- <div class="item" @click="changeSySMenu('facebook','/facebook')">
			<div class="name">
				<img :src="icon12" alt="">
				<span>facebook</span>
			</div>
		</div> -->
		<!-- <div class="item" @click="onAddSoft">
			<img :src="icon13" alt="">
			<span>添加平台</span>
		</div> -->
	</div>
	<div class="show_hide" @click="smallBigFun">
		<img :src="icon14" alt="">
		<span v-if="sideBigSmall">收起</span>
	</div>
</template>

<script setup>
import {
	ref,

	onMounted,
	onUnmounted,

} from 'vue'
import { v4 as uuidv4 } from 'uuid';
// 定义和接收 props
const props = defineProps({
	softName: {
		type: String,
		required: true,
	},
	mainAccountId: {
		type: number,
		required: true,
	},
});
import { ElMessage } from 'element-plus'
import { getOpenListSql } from "../api/account.js"
// 导入router
import { useRouter, useRoute } from 'vue-router'  // 导入 useRouter
import { number } from 'echarts';
const router = useRouter()  // 获取路由实例
const route = useRoute();  // 获取当前路由对象
// 导入图片
const icon10 = new URL('../assets/img/icon10.png', import.meta.url).href;
const icon11 = new URL('../assets/img/icon11.png', import.meta.url).href;
const icon12 = new URL('../assets/img/icon12.png', import.meta.url).href;
const icon13 = new URL('../assets/img/icon13.png', import.meta.url).href;
const icon14 = new URL('../assets/img/icon14.png', import.meta.url).href;
const baseUrl = ref(import.meta.env.VITE_BASE_API)
const mAccountId = ref()
onMounted(() => {
	setTimeout(function () {
		mAccountId.value = props.mainAccountId
		getOpenList()
	}, 500)
})
// 获取账号列表
const openList = ref([])
const getOpenList = () => {
	getOpenListSql({}).then(response => {
		if (response.code == 1) {
			openList.value = response.data
			changeSoftTab()
		}
	})

}
// 使用 afterEach 导航守卫
const nowPathName = ref(null);
if (route.matched[0]) {
	nowPathName.value = ref(route.matched[0].path)
}
// 调用父级方法
const emit = defineEmits(['changeChat', 'showHideWordFun', 'getSoftList']);
// 切换主账号方法 -- telegram和whatsapp页面调用
const changeSoftTab = () => {
	// selState.value = item.id
	let selAccount = '';
	let accountArr = []
	for (let i = 0; i < openList.value.length; i++) {
		if (openList.value[i].platform == props.softName) {
			for (let j = 0; j < openList.value[i].lists.length; j++) {
				accountArr = openList.value[i].lists
				if (openList.value[i].lists[j].id == mAccountId.value) {
					selAccount = openList.value[i].lists[j]
				}
			}
		}
	}
	emit('getSoftList', accountArr, selAccount);
}
// 调转页面
const changeSySMenu = (type, url, is_son) => {
	if (nowPathName.value != '/account' && nowPathName.value != '/telegram' && nowPathName.value != '/whatsapp' && nowPathName.value != '/facebook') {
		router.push({
			path: url,
			query: { 'chat': type }
		})
	} else if (type == 'index') {
		router.push({
			path: url,
			query: { 'chat': type }
		})
	} else {
		// 打开的页面为当前页面--只能是翻译页面  telegram或whatsapp

		if (nowPathName.value == '/account') {
			if (type == 'whatsapp' || type == 'telegram' || type == 'facebook') {
				emit('changeChat', type);
			} else {
				router.push({
					path: url,
					query: { 'chat': type }
				})
			}
		} else if (nowPathName.value == '/whatsapp' || nowPathName.value == '/telegram' || nowPathName.value == '/facebook') {
			if (props.softName == is_son) {
				mAccountId.value = type
				changeSoftTab()
			} else {
				router.push({
					path: url,
					query: { 'chat': type }
				})
			}
		} else {
			router.push({
				path: url,
				query: { 'chat': type }
			})
		}

	}
}
// 收起侧边栏 放开侧边栏 true 是大的
const sideBigSmall = ref(true)
const smallBigFun = () => {
	sideBigSmall.value = !sideBigSmall.value
	emit('showHideWordFun', sideBigSmall.value)
}

// 暴露方法给父组件
defineExpose({
	getOpenList, // 获取账号列表

});
</script>

<style>
@import url("../assets/style/tabar.scss");
</style>