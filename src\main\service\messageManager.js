import { app, ipcMain } from 'electron'
import { GlobalObject } from '../global'
import config from '../config'
import { platform } from '@electron-toolkit/utils'

export class MessageManager {
  count = 0
  messageMap = new Proxy(
    {},
    {
      set: (target, field, value) => {
        target[field] = value
        this.watchMessage(target)
        return true
      }
    }
  )

  getCount() {
    return this.count
  }
  setMessageCount(params) {
    const { platform, unReadCount } = params
    this.messageMap[platform] = { unReadCount }
    console.log('messageMap', this.messageMap, params)
  }
  watchMessage(target) {
    let c = Object.values(this.messageMap)
      .map((item) => item.unReadCount)
      .reduce((pre, cur) => {
        console.log(pre, cur, 'precur\n')
        return pre + cur
      }, 0)

    this.count = c
    console.log(c, this.count, 'count\n')

    if (config.isShowMessage) {
      this.showMessageCount()
    } else {
      this.hideMessageCount()
    }
    GlobalObject.mainWindow.webContents.send('unReadMessageCountChange', target)
  }

  showMessageCount() {
    if (platform.isMacOS) {
      app.setBadgeCount(this.count)
    }
  }

  hideMessageCount() {
    try {
      if (platform.isMacOS) {
        app.setBadgeCount(0)
      }
    } catch (error) {
      console.log(error)
    }
  }
}
