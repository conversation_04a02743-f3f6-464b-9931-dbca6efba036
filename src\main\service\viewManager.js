import { preloadJsPathMap } from '../index.js'
import { GlobalObject } from '@main/global/index.js'
import electron, { app, dialog, session } from 'electron'
import loadingCssString from '@main/assets/loading.css?raw'
import contextMenuCssString from '@main/assets/contextMenu.css?raw'
import { platform } from '@electron-toolkit/utils'
export class ViewManager {
  constructor() {
    this.viewsMap = /* @__PURE__ */ new Map()
  }
  viewsMap
  setViewBounds(view, { x, y, width, height }) {
    view.setBounds({
      x,
      y,
      width,
      height
    })
  }
  addView(viewConfig) {
    try {
      let view
      if (!this.viewsMap.has(viewConfig.name)) {
        view = new ViewInfo({
          ...viewConfig,
          webPreferences: {
            sandbox: false,
            contextIsolation: true,
            media: true,
            preload: preloadJsPathMap.get(viewConfig.platform),
            session: session.fromPartition(`persist:account-${viewConfig.itemInfo.session_id}`)
          }
        })
        view.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
          callback(true) 
        })

        // view.webContents.session.setPermissionCheckHandler((webContents, permission) => {
        //   return true
        // })
        // 处理view
        ViewHandler.init(view)
        this.setViewBounds(view, { ...GlobalObject.viewBounds })
        console.log({ ...GlobalObject.viewBounds })
        view.webContents.loadURL(viewConfig.url).catch((err) => {
          console.log(err, 'loadurl error')
        })

        if (!app.isPackaged) {
          view.webContents.openDevTools()
        }
        this.viewsMap.set(viewConfig.name, view)
        if (!viewConfig.noAttach) {
          this.attachViewToWindow(view)
        }
      } else {
        let view = this.viewsMap.get(viewConfig.name)
        GlobalObject.mainWindow.contentView.addChildView(view)

      }
      return true
    } catch (error) {
      console.log(error)
      return false
    }
  }

  attachViewToWindow(view) {
    GlobalObject.mainWindow.contentView.addChildView(view)
  }

  getView(name) {
    return this.viewsMap.get(name)
  }
  deleteView(name) {
    return this.viewsMap.delete(name)
  }
}
class ViewInfo extends electron.WebContentsView {
  constructor(viewConfig) {
    super(viewConfig)
    this.name = viewConfig.name
    this.url = viewConfig.url
    this.itemInfo = viewConfig.itemInfo
  }
  itemInfo
  name
  url
}

export function onMainBwReisze(bw) {
  bw.on('resize', () => {
    GlobalObject.viewManager?.viewsMap.forEach((view) => {
      GlobalObject.viewManager?.setViewBounds(view, GlobalObject.viewBounds)
    })
  })
}

class ViewHandler {
  static init(view) {
    this.setUserAgent(view)
    this.setViewOpenHandler(view)
    this.insertCss(view)
  }

  static interceptUrlRegExpMap = new Map([
    ['facebook', [new RegExp('https://business.facebook.com/')]]
  ])

  static setViewOpenHandler(view) {
    const handler = (e) => {
      console.log(e)
      this.interceptUrlRegExpMap.forEach((v, k) => {
        if (k === view.itemInfo.platform) {
          v.forEach((r) => {
            if (r.test(e.url)) {
              e.preventDefault()
              dialog.showMessageBox({
                title: '提示',
                message: `当前url已被拦截:\n ${r.toString().split('\\').join('')}`
              })
            }
          })
        }
      })
    }
    view.webContents.on('will-navigate', handler)
    view.webContents.on('new-window', handler)
  }

  static setUserAgent(view) {
    let us = ''

    if (platform.isMacOS) {
      us =
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
    } else {
      us =
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 '
    }
    view.webContents.setUserAgent(us)
  }

  static insertCss(view) {
    view.webContents.on('dom-ready', () => {
      view.webContents.insertCSS(loadingCssString)
      view.webContents.insertCSS(contextMenuCssString)
      console.log('view load success')
    })
  }
}
