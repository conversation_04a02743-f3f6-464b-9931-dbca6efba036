.sys_base {
  .sys_left_b {
    width: 85px;
  }
  .small_tab {
    margin-left: 102px !important;
    width: calc(100% - 112px) !important;
    .all_translate_cont {
      width: calc(100% - 0px) !important;
    }
  }
}
.all_translate_main_cont {
  display: flex;
  height: 100%;
  width: calc(100% - 122px) !important;
  .add_btn {
    color: #000;
    line-height: 40px;
    background-color: #f00;
  }
  .all_translate_cont {
    width: calc(100% - 110px);
    border-radius: 10px;
    overflow-y: auto;
    background-color: var(--bg-color-cont);
    // transition: all 0.3s;
    padding: 20px;
    .tit {
      display: flex;
      align-items: center;
      justify-content: space-between;
      h2 {
        font-family: Nunito Sans;
        font-size: 24px;
        font-weight: 700;
        line-height: 32px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: var(--font-color-login);
      }
      span {
        font-family: Nunito Sans;
        font-size: 16px;
        font-weight: 400;
        line-height: 21.33px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: var(--border-color-translate);
      }
      .tit_open {
        .el-form-item__label {
          color: #626da1;
        }
      }
    }
    .set_all_translate {
      height: calc(100% - 60px);
      width: 100%;
      display: flex;
      .trans_left {
        width: 50%;
        height: 100%;
        overflow-y: aut;
        border-right: 1px solid var(--border-color-translate);
        padding-right: 30px;
        .lang_translage {
          display: flex;
          align-items: center;
          padding: 20px 0;
          justify-content: center;
          .select_info {
            width: calc(50% - 20px);
            .el-select__wrapper {
              height: 45px;
              width: 100%;
              border-radius: 10px;
              font-size: 16px;
              background-color: var(--bg-color-index-echars);
              box-shadow: 0 0 0 transparent;
            }
            .el-select__placeholder {
              color: var(--font-color-index-echars);
            }
          }
          .trans {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 45px;
            padding: 0 20px;
            img {
              width: 37px;
              height: 37px;
            }
          }
        }
        // 翻译内容
        .trans_info {
          textarea {
            height: 155px;
            background-color: var(--bg-color-index-echars);
            box-shadow: 0 0 0 transparent;
            color: var(--font-color-index-echars);
          }
        }
        // 翻译线路
        .line_set {
          color: var(--font-color-login);
          padding-top: 30px;
          .all_tit {
            padding-bottom: 10px;
            .item {
              border: 0;
              padding-left: 0;
            }
            .item.on {
              border: 0;
            }
          }
          .items {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
          }
          .item {
            display: flex;

            align-items: center;
            width: 31%;
            border: 1px solid var(--border-color-translate);
            padding: 5px 10px;
            border-radius: 10px;
            margin-bottom: 3%;
            p {
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              border: 1px solid var(--border-color-translate);
              border-radius: 4px;
            }
            span {
              padding-left: 10px;
            }
          }
          .item.on {
            border: 1px solid #2072f7;
            p {
              border: 1px solid #2072f7;
              background-color: #2072f7;
            }
            span {
              color: #2072f7;
            }
          }
          .item:nth-child(3n + 2) {
            margin: 0 3.5% 3%;
          }
        }
        .trans_btn {
          background-color: #2072f7;
          color: #fff;
          width: 100%;
          height: 44px;
          border-radius: 10px;
          line-height: 44px;
          text-align: center;
          margin-top: 30px;
        }
      }
      .trans_right {
        width: 50%;
        padding-left: 30px;
        overflow-y: auto;
        color: #626da1;
        .items {
          padding-bottom: 20px;
          .item {
            .tit {
              color: #626da1;
              font-family: Inter;
              font-size: 18px;
              font-weight: 400;
              line-height: 25px;
              letter-spacing: 0.035em;
              padding-bottom: 10px;
              justify-content: flex-start;
              span {
                padding-right: 15px;
              }
            }
            .trans_c {
              border: 1px solid var(--border-color-translate);
              min-height: 50px;
              line-height: 50px;
              border-radius: 10px;
              padding: 0 10px;
              color: var(--font-color-login);
              font-size: 20px;
              p:nth-child(2) {
                border-top: 1px solid #eee;
              }
            }
          }
        }
      }
    }
  }
}
