import { Platform } from '.'

const f = {
  userElSelector: '[class="nameTag__37e49 canCopy__37e49"] .panelTitleContainer__37e49',
  inputElSelector: `div[class="textArea__74017 textAreaSlate__74017 slateContainer_ec4baf"] [contenteditable]`,
  inputTextElSelector: '[data-slate-string="true"]',

  sessionUserNameElSelector:
    'div[class="css-146c3p1 r-dnmrzs r-1udh08x r-1udbk01 r-3s2u2q r-bcqeeo r-1ttztb7 r-qvutc0 r-37j5jr r-a023e6 r-rjixqe r-16dba41 r-18u37iz r-1wvb978"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',
  currentSessionUserNameElSelector: ` h2 ~ li .selected_bf202d a`,
  messageListElSelector: 'ol',

  unreadElSelector: '[aria-label="私信"] .listItem__650eb .hiddenVisually__27f77',
  sessionElSelector: 'div[role="tablist"]',

  sendButtonElSelector:
    'button[class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1ez5h0i r-2yi16 r-1qi8awa r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l"]',
  sendButtonContainerElSelector: `[class="css-175oi2r r-1awozwy r-1sw30gj r-1867qdf r-18u37iz r-l00any r-jusfrs r-tuq35u r-1h0ofqe"]`,

  reciveMessageElSelector:
    'div[class="css-175oi2r r-1habvwh r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',
  messageElSelector: `[class="markup__75297 messageContent_c19a55"]`,
  sendMessageElSelector:
    'div[class="css-175oi2r r-obd0qt r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]'
}
export class DiscordHandler extends Platform {
  constructor(platform) {
    super(platform)
  }

  async getUserId() {
    if (!this.userId) {
      let userIdEl = document.querySelector(f.userElSelector)
      if (userIdEl) {
        // const m = /^.*(?=,)/.exec(userIdEl.getAttribute('aria-label'))
        this.userId = userIdEl.innerText
      }
      if (this.userId) {
        let params = {
          userId: this.userId,
          platform: this.platform,
          phone: this.userId,
          nickName: this.userName ? this.userName : this.userId,
          session_id: this.viewSessionId
        }
        this.sendPlatformUserInfo(params)
      }
    }
  }
  userId
  chatUserId
  userName

  async init(translater) {
    await super.init(translater)
    this.getUserId()
    this.bindInputFunction()
  }
  _o(mutations, observer) {
    if (/\/channels\/?/.test(location.pathname)) {
      // 绑定 input
      this.bindInputFunction()
      // 聊天页面
      this.translateList()
      // 获取未读消息的会话
      this.getUnreadSessionUserToFans()
      // 获取主账号信息
      this.getUserId()
      this.getCurrentSessionUserId()
    }
  }

  _u(location) {
    this.getCurrentSessionUserId()
  }

  getCurrentSessionUserId() {
    let count = 0
    const func = () => {
      let currentSessionUserNameEl = document.querySelector(f.currentSessionUserNameElSelector)
      if (currentSessionUserNameEl && count < 3) {
        this.chatUserId = currentSessionUserNameEl.innerText

        let params = {
          mainAccount: this.userId,
          fansId: this.chatUserId,
          nickName: this.chatUserId,
          platform: this.platform
        }
        this.sendCurrentSessionFansInfo(params)
      } else {
        setTimeout(() => {
          count++
          window.requestAnimationFrame(func)
        }, 500)
      }
    }
    window.requestAnimationFrame(func)
  }

  getUnreadSessionUserToFans() {
    let unreadCount = 0
    const unreadListInfo = []

    const unreadElList = document.querySelectorAll(f.unreadElSelector).values().toArray()

    unreadCount = unreadElList.length

    unreadElList.forEach((el) => {
      try {
        if (el.hasAttribute('aira-isread')) {
          return
        } else {
          el.setAttribute('aira-isread', 'true')
          const m = /.*(?=,)/.exec(el.innerText)
          let nickName = m[0].trimEnd()
          unreadListInfo.push({
            id: nickName,
            nickName
          })
        }
      } catch (error) {
        console.log('error %s', error.toString())
      }
    })

    if (unreadCount > 0) {
      this.sendUnReadCount(unreadCount)
    }
    if (unreadListInfo.length > 0) {
      this.sendNewFansList({
        viewSessionId: this.viewSessionId,
        platform: this.platform,
        mainAccount: this.userId,
        unreadListInfo
      })
    }
  }

  bindInputFunction() {
    const func = () => {
      const inputEL = document.querySelector(f.inputElSelector)
      if (inputEL) {
        if (inputEL.getAttribute('aria-bind')) {
          return
        }

        inputEL.addEventListener(
          'click',
          (e) => {
            this.changeInputEditStatus(false, f.inputElSelector)

            this.createMaskDiv()
          },
          true
        )
        inputEL.addEventListener(
          'keydown',
          (e) => {
            if (!((e.key === 'Enter' || e.keyCode === 13) && !e.shiftKey)) {
              this.changeInputEditStatus(false, f.inputElSelector)

              this.createMaskDiv()
            }
          },
          true
        )
        inputEL.setAttribute('aria-bind', 'true')
      } else {
        setTimeout(() => {
          window.requestAnimationFrame(func)
        }, 500)
      }
    }
    window.requestAnimationFrame(func)
  }

  sendMessageToInput({ message, type }) {
    this.inputMessage(message, type === 'send')
  }

  replaceContentEditableText(element, newValue) {
    const selection = window.getSelection()
    let originalRange

    if (selection.rangeCount > 0) {
      originalRange = selection.getRangeAt(0)
    } else {
      originalRange = document.createRange()
      originalRange.selectNodeContents(element)
    }

    originalRange.deleteContents()
    const textNode = document.createTextNode(newValue)
    originalRange.insertNode(textNode)

    originalRange.setStartAfter(textNode)
    originalRange.collapse(true)

    selection.removeAllRanges()
    selection.addRange(originalRange)

    const beforeInputEvent = new InputEvent('beforeinput', {
      bubbles: true,
      cancelable: true,
      data: newValue,
      inputType: 'insertText'
    })
    element.dispatchEvent(beforeInputEvent)

    const inputEvent = new InputEvent('input', {
      bubbles: true,
      cancelable: true,
      data: newValue,
      inputType: 'insertText'
    })
    element.dispatchEvent(inputEvent)

    const reactFiber = this.getReactFiber(element)
    if (reactFiber && reactFiber.memoizedProps) {
      if (typeof reactFiber.memoizedProps.onBeforeInput === 'function') {
        const syntheticEvent = this.createSyntheticInputEvent('beforeinput', newValue)
        reactFiber.memoizedProps.onBeforeInput(syntheticEvent)
      }

      if (typeof reactFiber.memoizedProps.onInput === 'function') {
        const syntheticEvent = this.createSyntheticInputEvent('input', newValue)
        reactFiber.memoizedProps.onInput(syntheticEvent)
      }
    }
  }

  createSyntheticInputEvent(type, data) {
    return {
      type,
      target: { value: data },
      currentTarget: { textContent: data },
      data,
      inputType: 'insertText',
      bubbles: true,
      cancelable: true,
      persist: () => {},
      preventDefault: () => {},
      stopPropagation: () => {}
    }
  }

  getReactFiber(node) {
    return node[
      Object.keys(node).find(
        (key) => key.startsWith('__reactFiber') || key.startsWith('__reactInternalInstance')
      )
    ]
  }

  inputMessage(value, is_send = false) {
    return new Promise(async (resolve) => {
      let element = document.querySelector(f.inputElSelector)
      if (element) {
        const clipboardData = new DataTransfer()
        clipboardData.setData('text/plain', value)
        const pasteEvent = new ClipboardEvent('paste', {
          bubbles: true,
          cancelable: true,
          clipboardData
        })
        element.dispatchEvent(pasteEvent)

        if (is_send) {
          const enterEvent = new KeyboardEvent('keydown', {
            key: 'Enter', // 键名
            keyCode: 13, // 键码（兼容旧浏览器）
            code: 'Enter', // 物理键代码
            bubbles: true, // 事件是否冒泡
            cancelable: true, // 事件是否可取消
            ctrlKey: false, // Ctrl键是否按下
            altKey: false, // Alt键是否按下
            shiftKey: false, // Shift键是否按下
            metaKey: false, // Meta键（Windows键/Mac Command键）是否按下
            repeat: false // 是否为重复按键
          })
          Object.assign(enterEvent, {
            isCustom: true
          })
          setTimeout(() => {
            element.dispatchEvent(enterEvent)
            element.blur()
            resolve()
          }, 50)
        }
      }
    })
  }

  switchShadowState(flag) {
    let m = document.querySelector('#myShadow')
    if (!m) {
      return
    }

    if (flag) {
      m.style.display = 'block'
    } else {
      m.style.display = 'none'
    }
  }

  setCaretToEnd(element) {
    const range = document.createRange()
    const selection = window.getSelection()
    range.selectNodeContents(element)
    range.collapse(false) // false 表示将光标放在内容末尾
    selection.removeAllRanges()
    selection.addRange(range)
    element.focus()
  }

  createMaskDiv() {
    const maskDiv = document.querySelector('#myShadow')
    if (!maskDiv) {
      let input = document.querySelector(f.inputElSelector)
      // 创建遮罩---禁止点击按钮
      let shadwoDiv = input.cloneNode()
      shadwoDiv.id = 'myShadow'
      shadwoDiv.style.position = 'absolute'
      shadwoDiv.style.width = '100%'
      shadwoDiv.style.height = '100%'
      shadwoDiv.style.top = '0px'
      shadwoDiv.style.margin = '0px'

      shadwoDiv.removeAttribute('data-slate-editor')
      shadwoDiv.removeAttribute('data-slate-node')
      shadwoDiv.style.background = 'var(--chat-background-default)'
      shadwoDiv.style.zIndex = 9999
      shadwoDiv.setAttribute('contenteditable', 'true')

      let maskContainer = input.closest(
        `[class='textArea__74017 textAreaSlate__74017 slateContainer_ec4baf'] > div`
      )
      if (maskContainer) {
        maskContainer.style.position = 'relative'
        maskContainer.appendChild(shadwoDiv)
        this.setCaretToEnd(shadwoDiv)
      } else {
        return
      }

      shadwoDiv.addEventListener('keydown', async (event) => {
        console.log(event)
        if ((event.key === 'Enter' || event.keyCode === 13) && !event.shiftKey) {
          event.stopPropagation()
          event.preventDefault()

          this.changeInputEditStatus(false, '#myShadow')
          let tV = await this.translater.translateInput(shadwoDiv.innerText)
          await this.inputMessage(tV ? tV : shadwoDiv.innerText, true)
          shadwoDiv.innerText = ''
          this.changeInputEditStatus(true, '#myShadow')
          this.changeInputEditStatus(true, f.inputElSelector)
          this.switchShadowState(false)
        }
        return
      })
    } else {
      this.setCaretToEnd(maskDiv)
      this.switchShadowState(true)
    }
  }

  changeInputEditStatus(editStatus, selector) {
    try {
      let element = document.querySelector(selector)

      if (!element) return
      if (editStatus) {
        element.setAttribute('contenteditable', 'true')
      } else {
        element.setAttribute('contenteditable', 'false')
      }
    } catch (error) {
      console.error(error)
    }
  }

  async translateList() {
    let listEL = document.querySelector(f.messageListElSelector)
    if (listEL) {
      let messages = listEL.querySelectorAll(f.messageElSelector)

      let messagetype = null
      messages.forEach((el) => {
        let img = el.parentNode.querySelector(':scope > img')
        if (img) {
          let id = img.src.match(/(?<=avatars\/)\d*(?=\/)/)[0]
          messagetype = id !== this.userId ? 'in' : 'out'
        }
        this.translater.translateMessage(el, { type: messagetype })
      })
    }
  }
}
