# WebAssembly (WASM) 问题解决方案

## 问题描述
在 Electron 应用中使用 Lottie 动画时遇到 WebAssembly 相关的 Content Security Policy (CSP) 错误：

```
WebAssembly.instantiateStreaming(): Refused to compile or instantiate WebAssembly module because 'unsafe-eval' is not an allowed source of script in the following Content Security Policy directive: "script-src 'self'"
```

## 解决方案

### 1. 主进程配置 (src/main/service/window.js)

**修改内容：**
- 添加了 CSP 头部配置，允许 WebAssembly 执行
- 在 BrowserWindow 的 webPreferences 中添加了 WASM 支持选项

**关键配置：**
```javascript
// 配置CSP策略以支持WebAssembly
session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
  callback({
    responseHeaders: {
      ...details.responseHeaders,
      'Content-Security-Policy': [
        "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; " +
        "worker-src 'self' blob:; " +
        // ... 其他策略
      ]
    }
  })
})

// BrowserWindow webPreferences
webPreferences: {
  webSecurity: false, // 允许跨域和WASM
  allowRunningInsecureContent: true, // 允许不安全内容
  experimentalFeatures: true // 启用实验性功能
}
```

### 2. 渲染进程配置

**HTML CSP 策略 (src/renderer/index.html)：**
更新了 Content-Security-Policy meta 标签，添加了对 WebAssembly 的支持。

**WASM 加载优化 (src/renderer/src/main.js)：**
- 添加了错误处理和异步初始化
- 创建了专门的 WASM 加载工具

### 3. 构建配置 (electron.vite.config.mjs)

**添加的配置：**
```javascript
assetsInclude: ['**/*.lottie', '**/*.wasm'],
optimizeDeps: {
  exclude: ['@lottiefiles/dotlottie-vue']
},
server: {
  headers: {
    'Cross-Origin-Embedder-Policy': 'require-corp',
    'Cross-Origin-Opener-Policy': 'same-origin'
  }
}
```

### 4. 新增工具文件

**WASM 加载工具 (src/renderer/src/utils/wasmLoader.js)：**
- 提供 WASM 初始化和验证功能
- 检测 Electron 环境
- 设置适当的 CSP 策略

**WASM 测试工具 (src/renderer/src/utils/wasmTest.js)：**
- 提供完整的 WASM 功能测试
- 生成详细的测试报告
- 开发环境下自动添加测试按钮

## 使用方法

### 1. 重新启动应用
```bash
npm run dev
```

### 2. 检查控制台输出
应用启动后，控制台会显示：
- WASM 初始化状态
- 自动测试结果
- 环境信息

### 3. 使用测试按钮（开发环境）
在开发环境下，页面右上角会出现"测试 WASM 功能"按钮，点击可以运行完整测试。

### 4. 验证 Lottie 动画
如果 WASM 配置正确，Lottie 动画应该能够正常加载和播放。

## 测试组件

创建了一个专门的测试组件 `WasmTest.vue`，可以添加到你的路由中进行详细测试：

```vue
<template>
  <WasmTest />
</template>

<script setup>
import WasmTest from '@/components/WasmTest.vue'
</script>
```

## 故障排除

### 1. 如果仍然出现 CSP 错误
- 检查浏览器开发者工具的 Console 和 Network 标签
- 确认 WASM 文件路径正确
- 验证 CSP 策略是否正确应用

### 2. 如果 WASM 文件加载失败
- 检查文件路径：`src/renderer/public/wasm/dotlottie-player.wasm`
- 确认文件存在且可访问
- 检查文件大小和格式

### 3. 如果 Lottie 动画不显示
- 运行 WASM 测试确认基础功能正常
- 检查 Lottie 文件格式和路径
- 查看控制台是否有相关错误信息

## 安全注意事项

本解决方案为了支持 WebAssembly 放宽了一些安全策略：
- `'unsafe-eval'` - 允许动态代码执行（WASM 需要）
- `webSecurity: false` - 禁用了一些 Web 安全检查

在生产环境中，建议：
1. 仅在必要时启用这些选项
2. 定期审查和更新安全策略
3. 考虑使用更严格的 CSP 策略，仅允许特定的 WASM 源

## 文件清单

修改的文件：
- `src/main/service/window.js` - 主进程窗口配置
- `src/main/index.js` - 主进程启动配置
- `src/renderer/index.html` - HTML CSP 策略
- `src/renderer/src/main.js` - 渲染进程入口
- `electron.vite.config.mjs` - 构建配置

新增的文件：
- `src/renderer/src/utils/wasmLoader.js` - WASM 加载工具
- `src/renderer/src/utils/wasmTest.js` - WASM 测试工具
- `src/renderer/src/components/WasmTest.vue` - 测试组件
- `WASM_FIX_README.md` - 本说明文档

## 版本兼容性

此解决方案适用于：
- Electron 31.x
- Vue 3.x
- @lottiefiles/dotlottie-vue 0.8.x
- electron-vite 2.x

如果你使用的是不同版本，可能需要相应调整配置。
