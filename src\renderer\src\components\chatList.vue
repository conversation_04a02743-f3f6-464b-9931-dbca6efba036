<template>
	<div class="sys_menu">
		<div :class="props.softName == '/' ? 'item on' : 'item'" @click="changeSySMenu('index', '/')">
			<img :src="icon10" alt="">
			<span v-if="sideBigSmall">首页</span>
		</div>
		<div v-for="(item, index) in chatList" :class="selChat.id == item.id ? 'item on' : 'item'"
			@click="changeSoftTab(item)">
			<img :src="item.platform == 'whatsapp' ? icon11 : icon12" alt="">
			<span v-if="sideBigSmall">{{ item.account_id ? item.account_id : item.platform }}</span>
		</div>
	</div>
	<div class="show_hide" @click="smallBigFun">
		<img :src="icon14" alt="">
		<span v-if="sideBigSmall">收起</span>
	</div>
</template>

<script setup>
import {
	ref,
	onMounted,
} from 'vue'
import { v4 as uuidv4 } from 'uuid';
// 定义和接收 props
const props = defineProps({
	chatList: {
		type: Array,
		required: true,
	},
	softName: {
		type: String,
		required: true,
	},
});
import { ElMessage } from 'element-plus'
import { getFansInfoSql, isNumberOver, addChatInfo, delChatAccount } from "../api/account.js"
// 导入router
import { useRouter } from 'vue-router'  // 导入 useRouter
const router = useRouter()  // 获取路由实例
// 导入图片
const icon10 = new URL('../assets/img/icon10.svg', import.meta.url).href;
const icon11 = new URL('../assets/img/icon11.svg', import.meta.url).href;
const icon12 = new URL('../assets/img/icon12.svg', import.meta.url).href;
const icon13 = new URL('../assets/img/icon13.svg', import.meta.url).href;
const icon14 = new URL('../assets/img/icon14.svg', import.meta.url).href;
// 操作父级方法
const emit = defineEmits(['getSoftList', 'changeChat', 'showHideWordFun']);
const selChat = ref({})
const changeSoftTab = (item) => {
	// selState.value = item.id
	selChat.value = item
	emit('getSoftList', props.chatList, item);
	// this.$emit("getSoftList", list.value);
}
// 收起侧边栏 放开侧边栏 true 是大的
const sideBigSmall = ref(true)
const smallBigFun = () => {
	sideBigSmall.value = !sideBigSmall.value
	emit('showHideWordFun', sideBigSmall.value)
}

onMounted(() => {
	setTimeout(function () {
	}, 6000)
})
// 使用 afterEach 导航守卫
const nowPathName = ref(null)
router.afterEach((to, from) => {
	nowPathName.value = from.path
});
// 调转页面
const changeSySMenu = (type, url) => {
	if (nowPathName.value != '/account') {
		router.push({
			path: url,
			query: { 'chat': type }
		})
	} else {
		if (type == 'whatsapp' || type == 'telegram') {
			emit('changeChat', type);
		} else {
			router.push({
				path: url,
				query: { 'chat': type }
			})
		}
	}
}

</script>

<style>
@import url("../assets/style/tabar.scss");
</style>