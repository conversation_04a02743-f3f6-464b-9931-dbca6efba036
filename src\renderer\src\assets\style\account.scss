.account_main_cont {
  display: flex;
  height: 100%;
  .add_btn {
    color: #000;
    line-height: 40px;
    background-color: #f00;
  }
  .soft_set {
    width: 100px;
    min-width: 100px;
    background-color: var(--bg-color-cont);
    margin-left: 10px;
    border-radius: 10px;
    // transition: all 0.3s;
  }
  .open_soft_set {
    width: 450px !important;
  }
  .account_cont {
    width: calc(100% - 110px);
    border-radius: 10px;
    overflow-y: auto;
    background-color: var(--bg-color-cont);
    // transition: all 0.3s;
    padding: 20px;
    .chatTit {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: var(--font-color-login);
      .tit {
        h2 {
          font-family: Nunito Sans;
          font-size: 24px;
          font-weight: 700;
          line-height: 32px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          padding-bottom: 3px;
        }
        span {
          font-family: Nunito Sans;
          font-size: 16px;
          font-weight: 400;
          line-height: 21.33px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #2072f7;
        }
      }
      .btns {
        display: flex;
        .item {
          display: flex;
          background-color: #f1f1f1;
          margin: 0 10px;
          height: 35px;
          align-items: center;
          border-radius: 30px;
          line-height: 35px;
          text-align: center;
          padding: 0 20px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          img {
            width: 20px;
            height: 20px;
            margin-right: 15px;
          }
          p {
            width: Hug (109px) px;
            height: Hug (25px) px;
            top: 10px;
            left: 42px;
            gap: 14px;
            opacity: 0px;
          }
        }
        .items {
          display: flex;
          .item {
            color: #fff;
            background-color: #2072f7;
            box-shadow: 0px 5px 8px #8f95b226;
          }
          .item:nth-child(2) {
            background-color: #ffc800;
          }
          .item:nth-child(3) {
            background-color: #00b884;
          }
          .item:nth-child(4) {
            background-color: #fd71af;
          }
        }
      }
      .btns > .item {
        color: #000;
      }
      .clock {
        display: flex;
        align-items: center;
        span {
          padding-right: 8px;
          color: #8f95b2;
          font-family: Nunito Sans;
          font-size: 18.67px;
          font-weight: 400;
          line-height: 25.46px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
        }
      }
    }
    .chatList {
      padding-top: 30px;
      .el-table__header-wrapper {
        background-color: var(--bg-color-cont);
      }
      tr {
        background-color: var(--bg-color-cont);
        th.el-table__cell {
          background-color: var(--bg-color-index-echars);
          border-bottom: 0;
        }
        th.el-table__cell:nth-child(1) {
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
          border-left: 1px solid var(--bg-color-cont);
        }
        th.el-table__cell:last-child {
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
          border-right: 1px solid var(--bg-color-cont);
        }
        td.el-table__cell {
          border-bottom: 1px solid var(--bg-color-index-echars);
        }
        .el-table .cell {
          overflow: auto;
          border-bottom: 0;
        }
      }
      .el-table {
        // border-bottom: 1px solid var(--bg-color-index-echars)!important;
        --el-table-border-color: '';
        --el-table-bg-color: '';
      }
      .setBtn {
        background-color: #dde4ff;
        border-radius: 40px;
        color: #2072f7;
        box-shadow: 0px 8px 10px #8f95b226;
      }
      .setBtn.close {
        background-color: #f56c6c;
        color: #fff;
      }
      .setBtn.del {
        background-color: #fed4e7;
        color: #fd71af;
      }
    }
    .pageInfo {
      padding-top: 30px;
      .el-pagination {
        justify-content: center;
      }
    }
  }
  .open_account_cont {
    width: calc(100% - 460px) !important;
  }
}
