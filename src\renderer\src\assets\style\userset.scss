.user_set {
  display: flex;
  height: 100%;
  justify-content: flex-end;
  .set_cont {
    overflow: hidden;
    color: var(--font-color-login);
    h2 {
      font-family: Inter;
      font-size: 24px;
      font-weight: 600;
      letter-spacing: 0.035em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      padding-bottom: 20px;
    }
    // 翻译器设置
    .translate_set {
      height: 100%;
      overflow-y: auto;
      .select_info {
        margin-bottom: 20px;
        .label_tit {
          font-family: Inter;
          font-size: 16px;
          font-weight: 400;
          line-height: 25px;
          letter-spacing: 0.035em;
          text-align: left;
          padding-bottom: 4px;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #8f95b2;
          padding-bottom: 6px;
        }
        .el-select__wrapper {
          height: 45px;
          border-radius: 10px;
          font-size: 16px;
          background-color: var(--bg-color-index-echars);
          box-shadow: 0 0 0 transparent;
        }
        .el-select__placeholder {
          color: var(--font-color-index-echars);
        }
      }
      .line {
        border-bottom: 1px solid #dddfe7;
        padding-bottom: 20px;
      }
      .el-form-item__label {
        justify-content: space-between;
      }
    }
    // 快捷回复
    .quick_reply {
      overflow: auto;
      height: 100%;
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 40px;
        img {
          width: 30px;
          height: 30px;
        }
        .top_tit {
          display: flex;
          align-items: center;
          h2 {
            padding-bottom: 0;
            padding-right: 10px;
          }
        }
      }
      .quick_type {
        display: flex;
        align-items: center;
        border: 1px solid #2072f7;
        height: 48px;
        line-height: 48px;
        border-radius: 10px;
        .item {
          width: 50%;
          text-align: center;
          font-family: Inter;
          font-size: 16px;
          font-weight: 400;
          letter-spacing: 0.035em;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: var(--font-color-contact);
        }
        .item:first-child.on {
          background-color: #2072f7;
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
          color: #fff;
        }
        .item:last-child.on {
          background-color: #2072f7;
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
          color: #fff;
        }
      }
      .sendType {
        display: flex;
        align-items: center;
        padding: 40px 0;
        .item {
          width: 50%;
          display: flex;
          align-items: center;
          .icon {
            width: 24px;
            height: 24px;
            gap: 0px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #8f95b2;
            img {
              width: 13px;
              height: 11px;
            }
          }
          span {
            font-family: Inter;
            font-size: 16px;
            font-weight: 400;
            line-height: 20px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #8f95b2;
            padding-left: 12px;
          }
        }
        .item.on {
          .icon {
            background-color: #2072f7;
            border: 1px solid #2072f7;
          }
          span {
            color: #2072f7;
          }
        }
      }
      .search {
        width: 100%;
        // border: 1px solid #DDDFE7;
        height: 54px;
        border-radius: 10px;
        background-color: var(--bg-color-index-echars);
        .input {
          width: 100%;
          display: flex;
          align-items: center;
          img {
            width: 24px;
            height: 24px;
            margin-right: 20px;
          }
          input {
            border: 0;
            height: 52px;
            border-radius: 10px;
            width: calc(100% - 44px);
            padding: 0 15px;
            outline: 0;
            font-size: 16px;
            color: var(--font-color-index-echars);
            background-color: var(--bg-color-index-echars);
          }
        }
      }
      .quick_list {
        padding-top: 30px;
        .items {
          .tit {
            display: flex;
            align-items: center;
            padding-bottom: 25px;
            img {
              width: 30px;
              height: 30px;
            }
            span {
              font-family: Inter;
              font-size: 20px;
              font-weight: 400;
              line-height: 25px;
              letter-spacing: 0.035em;
              text-align: left;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
              color: #8f95b2;
              padding-left: 10px;
            }
          }

          .item {
            padding-left: 32px;
            .quick_infos {
              padding: 0px 0;
              .infos_item {
                border: 1px solid #dddfe7;
                margin-bottom: 20px;
                width: 100%;
                border-radius: 10px;
                padding: 15px;
                .btn {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  font-family: Inter;
                  font-size: 18px;
                  font-weight: 400;
                  line-height: 24px;
                  text-align: left;
                  text-underline-position: from-font;
                  text-decoration-skip-ink: none;
                  color: #8f95b2;
                  p {
                    border: 1px solid #dddfe7;
                    line-height: 30px;
                    height: 30px;
                    border-radius: 8px;
                    padding: 0 15px;
                    margin-left: 10px;
                  }
                }
                .quick_cont {
                  font-family: Inter;
                  font-size: 20px;
                  font-weight: 700;
                  line-height: 24px;
                  text-align: left;
                  text-underline-position: from-font;
                  text-decoration-skip-ink: none;
                  color: var(--font-color-login);
                  padding-top: 20px;
                }
              }
            }
          }
        }
      }
      .empty_quick {
        .empty {
          text-align: center;
          padding: 100px 0 0;
          p {
            font-family: Inter;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #8f95b2;
          }
          a {
            font-family: Inter;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #2072f7;
            text-decoration: none;
            padding-top: 6px;
          }
        }
      }
    }
    // 粉丝备注
    .fans_remark {
      // overflow-y: auto;
      height: 100%;
      position: relative;
      .fans_id {
        display: flex;
        border-bottom: 1px solid #dddfe7;
        padding-bottom: 15px;
        padding-top: 10px;
        margin-bottom: 15px;
        span {
          color: #8f95b2;
        }
        p {
          font-size: 16px;
          font-weight: 400;
          line-height: 25px;
          letter-spacing: 0.035em;
          color: var(--font-color-login);
          padding-right: 20px;
        }
      }
      .base_info {
        overflow-y: auto;
        height: calc(100% - 180px);
        h3 {
          font-family: Inter;
          font-size: 16px;
          font-weight: 700;
          line-height: 25px;
          letter-spacing: 0.035em;
          text-align: center;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: var(--font-color-login);
          padding-bottom: 20px;
        }
        .el-form-item__label {
          color: var(--font-color-login);
        }
        .input_info {
          margin-bottom: 20px;
          .label_tit {
            font-family: Inter;
            font-size: 16px;
            font-weight: 400;
            line-height: 25px;
            letter-spacing: 0.035em;
            text-align: left;
            padding-bottom: 4px;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #8f95b2;
            padding-bottom: 6px;
          }
          .el-input__wrapper {
            height: 45px;
            border-radius: 10px;
            font-size: 16px;
            background-color: var(--bg-color-index-echars);
            box-shadow: 0 0 0 transparent;
          }
        }
        .user_sex {
          display: flex;
          justify-content: space-between;
          .label_tit {
            font-family: Inter;
            font-size: 16px;
            font-weight: 400;
            line-height: 25px;
            letter-spacing: 0.035em;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #8f95b2;
          }
          .sex_info {
            display: flex;
            align-items: center;
            padding-bottom: 30px;
            .item {
              width: 70px;
              display: flex;
              align-items: center;
              align-items: center;
              p {
                width: 24px;
                height: 24px;
                border: 1px solid #8f95b2;
                border-radius: 6px;
                margin-right: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
              span {
                font-size: 16px;
                color: #8f95b2;
              }
            }
            .item.on {
              p {
                background-color: #2072f7;
                border: 1px solid #2072f7;
              }
              span {
                color: #2072f7;
              }
            }
          }
        }
        .select_info {
          margin-bottom: 20px;
          .label_tit {
            font-family: Inter;
            font-size: 16px;
            font-weight: 400;
            line-height: 25px;
            letter-spacing: 0.035em;
            text-align: left;
            padding-bottom: 4px;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #8f95b2;
            padding-bottom: 6px;
          }
          .el-select__wrapper {
            height: 45px;
            border-radius: 10px;
            font-size: 16px;
            background-color: var(--bg-color-index-echars);
            box-shadow: 0 0 0 transparent;
            .el-select__placeholder {
              color: var(--font-color-index-echars);
            }
          }
        }
        textarea {
          height: 85px;
          background-color: var(--bg-color-index-echars);
          box-shadow: 0 0 0 transparent;
          color: var(--font-color-index-echars);
        }
      }
      .save_fun {
        line-height: 44px;
        height: 44px;
        background-color: #2072f7;
        width: 100%;
        border-radius: 10px;
        position: absolute;
        bottom: 0;
        text-align: center;
        color: #fff;
      }
    }
    // 粉丝统计
    .fans_state {
      height: 100%;
      overflow-y: auto;
      .state_info {
        margin-bottom: 25px;
        .numbers {
          display: flex;
          align-items: center;
          p {
            font-family: Inter;
            font-size: 16px;
            font-weight: 400;
            line-height: 55px;
            letter-spacing: 0.035em;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #626da1;
            padding-right: 10px;
          }
          span {
            font-family: Inter;
            font-size: 20px;
            font-weight: 600;
            letter-spacing: 0.035em;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #2072f7;
          }
        }
      }
    }
  }
  .set_cont_show {
    width: 350px !important;
    padding: 20px;
    border-right: 1px solid #f7f7f7;
  }
  .infos {
    width: 100px;
    padding: 20px 0;
    height: 100%;
    overflow-y: auto;
    .item {
      text-align: center;
      padding: 20px 0;
      display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      img {
        width: 24px;
        height: 24px;
      }
      p {
        color: var(--font-color-login);
        font-family: Nunito Sans;
        font-size: 14px;
        font-weight: 400;
        line-height: 19.1px;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
      }
    }
    .item.on {
      p {
        color: #2072f7;
      }
    }
  }
}
