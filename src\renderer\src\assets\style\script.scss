.sys_base{
	.sys_left_b{
		width: 85px;
	}
	.small_tab{
		margin-left:102px!important;
		 width: calc(100% - 112px)!important;
		 .script_cont{
		 	width: calc(100% - 0px)!important;
		 }
	}
}
.script_main_cont{
	display: flex;
	height: 100%;
	width: calc(100% - 122px)!important;
	.add_btn{
		color:#000;
		line-height: 40px;
		background-color: #f00;
	}
	.script_cont{
		width: calc(100% - 110px);
		border-radius: 10px;
		overflow-y: auto;
		background-color: var(--bg-color-cont);
		transition: all 0.3s;
		padding:20px;
		.tit{
			display: flex;
			align-items: center;
			justify-content: space-between;
			h2{
				font-family: Nunito Sans;
				font-size: 24px;
				font-weight: 700;
				line-height: 32px;
				text-align: left;
				text-underline-position: from-font;
				text-decoration-skip-ink: none;
				color:var(--font-color-login);
			}
			span{
				font-family: Nunito Sans;
				font-size: 16px;
				font-weight: 400;
				line-height: 21.33px;
				text-align: left;
				text-underline-position: from-font;
				text-decoration-skip-ink: none;
				color:#DDDFE7;
			}
			.btns{
				display: flex;
				align-items: center;
				.item{
					background-color: #2072F7;
					line-height: 44px;
					height: 44px;
					padding:0 35px;
					border-radius: 30px;
					margin-left: 20px;
					font-size: 14px;
				}
				.item:last-child{
					background-color: #FD71AF;
				}
			}
		}
		.scriptList{
			padding-top:30px;
			.el-table__header-wrapper{
				background-color: var(--bg-color-cont);
			}
			tr{
				background-color: var(--bg-color-cont);
				th.el-table__cell{
					background-color:var(--bg-color-index-echars);
				}
				th.el-table__cell:nth-child(1){
					border-top-left-radius: 10px;
					border-bottom-left-radius: 10px;
					border-left:1px solid var(--bg-color-cont);
				}
				th.el-table__cell:last-child{
					border-top-right-radius: 10px;
					border-bottom-right-radius: 10px;
					border-right:1px solid var(--bg-color-index-echars);
				}
				td.el-table__cell{
					border-bottom:1px solid var(--bg-color-index-echars);
				}
				.el-table .cell{
					overflow: auto;
				}
			}
			td.el-table__cell{
				border-bottom:1px solid var(--bg-color-index-echars);
			}
			.el-table{
				border-bottom: 1px solid var(--bg-color-index-echars)!important;
				--el-table-border-color:'';
				--el-table-bg-color:''
			}
			.setBtn{
				background-color: #DDE4FF;
				border-radius: 40px;
				color:#2072F7;
				box-shadow: 0px 8px 10px #8F95B226;
			}
			.setBtn.del{
				background-color: #FED4E7;
				color:#FD71AF;
			}
		}
		.pageInfo{
			padding-top:30px;
			.el-pagination{
				justify-content: center;
			}
		}
	}
}