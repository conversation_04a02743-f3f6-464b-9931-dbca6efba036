<template>
	<div class="sys_base">
		<!-- 系统导航 -->
		<!-- <div :class="showHideWord?'sys_left':'sys_left sys_left_b'">
			<Tabar :softName='chatName' @changeChat='changeChat' @showHideWordFun='showHideWordFun'></Tabar>
		</div> -->
		<!-- 主要内容区 -->
		<div
			:class="tabarStore.showHideWord ? 'main_cont all_translate_main_cont' : 'main_cont all_translate_main_cont small_tab'">
			<!-- 聚合翻译 -->
			<div class="all_translate_cont">
				<div class="tit">
					<div class="tit_info">
						<h2>聚合翻译</h2>
						<span>Aggregate Translation</span>
					</div>
					<div class="tit_open">
						<el-form-item label="反向翻译对照">
							<el-switch v-model="allConfig.self" active-value='true' inactive-value='false'></el-switch>
						</el-form-item>
					</div>
				</div>

				<div class="set_all_translate">
					<div class="trans_left">
						<el-form ref="form" :model="allConfig">
							<!-- 翻译配置  -->
							<div class="lang_translage">
								<div class="select_info">

									<el-select v-model="allConfig.from_lang" placeholder="输入语言">
										<el-option v-for="item in allLang" :label="item.lang_name" :value="item.lang_code"></el-option>
									</el-select>
								</div>
								<div class="trans">
									<img :src="icon46" alt="">
								</div>
								<div class="select_info">

									<el-select v-model="allConfig.to_lang" placeholder="翻译语言">
										<el-option v-for="item in allLang" :label="item.lang_name" :value="item.lang_code"></el-option>
									</el-select>
								</div>
							</div>
							<!-- 翻译内容 -->
							<div class="trans_info">
								<el-input type="textarea" v-model="allConfig.desc"></el-input>
							</div>
							<!-- 线路配置 -->
							<div class="line_set">
								<div class="all_tit">
									<div :class="lineInfo.ptAllSel ? 'item on' : 'item'" @click="ptSelAllFun">
										<p>
											<img v-if="lineInfo.ptAllSel" :src="icon44" alt="">
										</p>
										<span>普通线路</span>
									</div>
								</div>
								<div class="items">
									<div v-for="(item, index) in lineInfo.pt" @click="ptChangeCellFun(index)"
										:class="item.sel ? 'item on' : 'item'">
										<p>
											<img v-if="item.sel" :src="icon44" alt="">
										</p>
										<span>{{ item.engine_name }}</span>
									</div>
								</div>
							</div>
							<!-- 装有线路 -->
							<div class="line_set">
								<div class="all_tit">
									<div :class="lineInfo.zyAllSel ? 'item on' : 'item'" @click="zySelAllFun">
										<p>
											<img v-if="lineInfo.zyAllSel" :src="icon44" alt="">
										</p>
										<span>专业线路</span>
									</div>
								</div>
								<div class="items">
									<div v-for="(item, index) in lineInfo.zy" @click="zyChangeCellFun(index)"
										:class="item.sel ? 'item on' : 'item'">
										<p>
											<img v-if="item.sel" :src="icon44" alt="">
										</p>
										<span>{{ item.engine_name }}</span>
									</div>
								</div>
							</div>
							<div class="trans_btn" @click="translateFun">翻译</div>
						</el-form>
					</div>
					<div class="trans_right">
						<div class="items" v-for="item in lineInfo.pt">
							<div class="item" v-if="item.sel && item.transInfo">
								<div class="tit">
									<span>{{ item.engine_name }}</span>
									<img @click="copyData(item.transInfo)" :src="icon58" alt="">
								</div>
								<div class="trans_c">
									<p>{{ item.transInfo }}</p>
									<p v-if="allConfig.self == 'true'">{{ allConfig.desc }}</p>
								</div>
							</div>
						</div>
						<div class="items" v-for="item in lineInfo.zy">
							<div class="item" v-if="item.sel && item.transInfo">
								<div class="tit">
									<span>{{ item.engine_name }}</span>
									<img @click="copyData(item.transInfo)" :src="icon58" alt="">
								</div>
								<div class="trans_c">
									<p>{{ item.transInfo }}</p>
									<p v-if="allConfig.self == 'true'">{{ allConfig.desc }}</p>
								</div>
							</div>
						</div>
					</div>
				</div>


			</div>
		</div>
	</div>

</template>

<script setup>
import { useTabStore } from "@renderer/store/index.js"
const tabarStore = useTabStore()
import {
	ref, watch,
	onMounted,
	onUnmounted
} from 'vue'
// import {
// 	getWorkOrderSql
// } from "../../api/account.js"
import {
	useRouter,
	useRoute
} from 'vue-router' // 导入 useRouter
const router = useRouter() // 获取路由实例
const route = useRoute(); // 获取当前路由对象
import { ElMessage } from 'element-plus'
import {
	getAllLangSql,
	getAllEngineSql,
	getQuickReplySql
} from "../../api/account.js"
import {
	translate
} from "../../api/translate.js"
// 导入本地图片
const icon44 = new URL('../../assets/img/icon44.png', import.meta.url).href;
const icon46 = new URL('../../assets/img/icon46.svg', import.meta.url).href;
const icon58 = new URL('../../assets/img/icon58.png', import.meta.url).href;
// 设置线路信息
const lineInfo = ref({
	'pt': [],
	'zy': [],
	'ptAllSel': false,
	'zyAllSel': false
})
// 普通设置全选
const ptSelAllFun = () => {
	lineInfo.value.ptAllSel = !lineInfo.value.ptAllSel
	// 全选设置
	let ptList = lineInfo.value.pt
	for (let i = 0; i < ptList.length; i++) {
		ptList[i].sel = lineInfo.value.ptAllSel
	}
	lineInfo.value.pt = ptList
}
// 普通选择渠道
const ptChangeCellFun = (index) => {
	let ptList = lineInfo.value.pt
	ptList[index].sel = !ptList[index].sel
	lineInfo.value.pt = ptList
	// 判单是否全选
	let is_all_sel = true
	for (let i = 0; i < ptList.length; i++) {
		if (!ptList[i].sel) {
			is_all_sel = false
		}
	}
	lineInfo.value.ptAllSel = is_all_sel
}
// 专线设置全选
const zySelAllFun = () => {
	lineInfo.value.zyAllSel = !lineInfo.value.zyAllSel
	// 全选设置
	let ptList = lineInfo.value.zy
	for (let i = 0; i < ptList.length; i++) {
		ptList[i].sel = lineInfo.value.zyAllSel
	}
	lineInfo.value.zy = ptList
}
// 专线选择渠道
const zyChangeCellFun = (index) => {
	let ptList = lineInfo.value.zy
	ptList[index].sel = !ptList[index].sel
	lineInfo.value.zy = ptList
	// 判单是否全选
	let is_all_sel = true
	for (let i = 0; i < ptList.length; i++) {
		if (!ptList[i].sel) {
			is_all_sel = false
		}
	}
	lineInfo.value.zyAllSel = is_all_sel
}
// 复制内容到剪切板
const copyData = async (data) => {
	try {
		await navigator.clipboard.writeText(data);
		ElMessage({
			showClose: true,
			message: '复制成功',
			type: 'success'
		})
	} catch (err) {
		ElMessage({
			showClose: true,
			message: '复制失败',
			type: 'info'
		})
	}
}
// 全局设置
const configSet = ref({})
// 改变平台
const changeChat = (cN) => {
	chatName.value = cN
}
// 设置是否显示设置信息页面
const is_open = ref(false)
const openSetPage = (son_b) => {
	is_open.value = son_b
}
// 全局翻译设置
const allConfig = ref({})

// 获取所有语言
const allLang = ref({})

function getAllLang() {
	getAllLangSql({}).then(response => {
		if (response.code == 1) {
			allLang.value = response.data
		}
	})
}
getAllLang()

// 获取所有翻译引擎
const allEngine = ref({})

function getAllEngine() {
	getAllEngineSql({}).then(response => {
		if (response.code == 1) {
			allEngine.value = response.data
			let engintPt = [];
			let engintZy = [];
			for (let i = 0; i < response.data.length; i++) {
				if (response.data[i].engine_type == 'general') {
					engintPt.push(response.data[i])
				}
				if (response.data[i].engine_type == 'profession') {
					engintZy.push(response.data[i])
				}
			}
			lineInfo.value.pt = engintPt
			lineInfo.value.zy = engintZy
		}
	})
}
getAllEngine()

const translateFun = () => {
	let allEngin = lineInfo.value.pt
	// if(!allConfig.value.from_lang){
	// 	ElMessage({
	// 		showClose: true,
	// 		message: '请选择输入语言',
	// 		type: 'warning'
	// 	})
	// 	return
	// }
	if(!allConfig.value.to_lang){
		ElMessage({
			showClose: true,
			message: '请选择翻译语言',
			type: 'warning'
		})
		return
	}
	let sel = false
	for (let i = 0; i < allEngin.length; i++) {
		if (allEngin[i] && allEngin[i].sel) {
			sel = true
			translate({
				'engineCode': allEngin[i].engine_code,
				'fromLang': allConfig.value.from_lang,
				'toLang': allConfig.value.to_lang,
				'text': allConfig.value.desc
			}).then(response => {
				if (response.code == 1) {
					allEngin[i].transInfo = response.data.result
				} else if (response.code == 400010) {
					ElMessage({
						showClose: true,
						message: response.msg,
						type: 'warning'
					})
				} else {
					ElMessage({
						showClose: true,
						message: response.msg === '翻译语言编码不能为空' ? '请设置翻译语言' : '翻译失败，请稍后重试',
						type: 'warning'
					})
				}
			})
		}
	}
	lineInfo.value.pt = allEngin
	let allEnginZy = lineInfo.value.zy
	for (let i = 0; i < allEngin.length; i++) {
		if (allEnginZy[i] && allEnginZy[i].sel) {
			sel = true
			translate({
				'engineCode': allEnginZy[i].engine_code,
				'fromLang': allConfig.value.from_lang,
				'toLang': allConfig.value.to_lang,
				'text': allConfig.value.desc
			}).then(response => {
				if (response.code == 1) {
					allEnginZy[i].transInfo = response.data.result
				}
			})
		}
	}
	lineInfo.value.zy = allEnginZy

	if(!sel){
		ElMessage({
			showClose: true,
			message: '请至少选择一条翻译线路',
			type: 'warning'
		})
	}
}
// 传递左侧栏目隐藏或展示文字方法
const showHideWord = ref(true)
watch(
	() => route.query,
	(newParams, oldParams) => {
		// 设置左侧是否显示
		if (newParams.chat == 'slide') {
			showHideWord.value = newParams.type == 'true' ? true : false
		}
	},
	{ immediate: true } // 立即执行一次，以确保初始参数也被捕获
);
</script>

<style lang="scss">
@import url("../../assets/style/all_translate.scss");
</style>