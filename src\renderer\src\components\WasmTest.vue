<template>
  <div class="wasm-test">
    <h3>WebAssembly 测试</h3>
    <div class="status">
      <p>WebAssembly 支持: <span :class="wasmSupported ? 'success' : 'error'">{{ wasmSupported ? '✓' : '✗' }}</span></p>
      <p>WASM 文件状态: <span :class="wasmStatus.class">{{ wasmStatus.text }}</span></p>
      <p>Lottie WASM 状态: <span :class="lottieStatus.class">{{ lottieStatus.text }}</span></p>
    </div>
    
    <div class="test-section" v-if="wasmSupported">
      <h4>Lottie 动画测试</h4>
      <div class="lottie-container">
        <dotlottie-vue 
          v-if="showLottie"
          :src="testLottieUrl" 
          autoplay 
          loop
          style="width: 200px; height: 200px;"
          @load="onLottieLoad"
          @error="onLottieError"
        />
        <div v-else class="placeholder">
          <p>Lottie 动画加载中...</p>
        </div>
      </div>
      
      <button @click="testWasm" :disabled="testing">
        {{ testing ? '测试中...' : '测试 WASM 功能' }}
      </button>
    </div>

    <div class="error-info" v-if="errorMessage">
      <h4>错误信息:</h4>
      <pre>{{ errorMessage }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { DotLottieVue } from '@lottiefiles/dotlottie-vue'
import { initializeWasm, isElectronEnvironment, getWasmConfig } from '../utils/wasmLoader.js'

const wasmSupported = ref(false)
const wasmStatus = ref({ text: '检查中...', class: 'pending' })
const lottieStatus = ref({ text: '等待中...', class: 'pending' })
const showLottie = ref(false)
const testing = ref(false)
const errorMessage = ref('')

// 测试用的 Lottie URL（你可以替换为实际的 .lottie 文件）
const testLottieUrl = ref('https://lottie.host/4f5c1b9b-2ab5-4f8e-8b5a-9d8c7e6f5a4b/abc123def456.lottie')

onMounted(async () => {
  await checkWasmSupport()
})

async function checkWasmSupport() {
  try {
    // 检查基本 WebAssembly 支持
    wasmSupported.value = typeof WebAssembly !== 'undefined'
    
    if (!wasmSupported.value) {
      wasmStatus.value = { text: '不支持 WebAssembly', class: 'error' }
      return
    }

    // 检查环境信息
    const isElectron = isElectronEnvironment()
    const config = getWasmConfig()
    
    console.log('Environment info:', {
      isElectron,
      config,
      userAgent: navigator.userAgent
    })

    // 尝试加载 WASM 文件
    const wasmUrl = new URL('../public/wasm/dotlottie-player.wasm', import.meta.url).href
    const wasmInitialized = await initializeWasm(wasmUrl)
    
    if (wasmInitialized) {
      wasmStatus.value = { text: '✓ WASM 文件加载成功', class: 'success' }
      lottieStatus.value = { text: '准备测试 Lottie', class: 'pending' }
      
      // 延迟显示 Lottie 组件
      setTimeout(() => {
        showLottie.value = true
      }, 1000)
    } else {
      wasmStatus.value = { text: '✗ WASM 文件加载失败', class: 'error' }
      lottieStatus.value = { text: '无法测试 Lottie', class: 'error' }
    }
  } catch (error) {
    console.error('WASM support check failed:', error)
    errorMessage.value = error.message
    wasmStatus.value = { text: '✗ 检查失败', class: 'error' }
  }
}

async function testWasm() {
  testing.value = true
  try {
    // 创建一个简单的 WASM 模块进行测试
    const wasmCode = new Uint8Array([
      0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00,
      0x01, 0x07, 0x01, 0x60, 0x02, 0x7f, 0x7f, 0x01, 0x7f,
      0x03, 0x02, 0x01, 0x00,
      0x07, 0x07, 0x01, 0x03, 0x61, 0x64, 0x64, 0x00, 0x00,
      0x0a, 0x09, 0x01, 0x07, 0x00, 0x20, 0x00, 0x20, 0x01, 0x6a, 0x0b
    ])

    const wasmModule = await WebAssembly.instantiate(wasmCode)
    const result = wasmModule.instance.exports.add(5, 3)
    
    if (result === 8) {
      lottieStatus.value = { text: '✓ WASM 功能正常', class: 'success' }
    } else {
      throw new Error(`WASM 计算错误: 期望 8, 得到 ${result}`)
    }
  } catch (error) {
    console.error('WASM test failed:', error)
    errorMessage.value = error.message
    lottieStatus.value = { text: '✗ WASM 测试失败', class: 'error' }
  } finally {
    testing.value = false
  }
}

function onLottieLoad() {
  console.log('Lottie animation loaded successfully')
  lottieStatus.value = { text: '✓ Lottie 动画加载成功', class: 'success' }
}

function onLottieError(error) {
  console.error('Lottie animation error:', error)
  errorMessage.value = `Lottie 错误: ${error.message || error}`
  lottieStatus.value = { text: '✗ Lottie 动画加载失败', class: 'error' }
}
</script>

<style scoped>
.wasm-test {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.status p {
  margin: 8px 0;
  font-family: monospace;
}

.success {
  color: #4caf50;
  font-weight: bold;
}

.error {
  color: #f44336;
  font-weight: bold;
}

.pending {
  color: #ff9800;
  font-weight: bold;
}

.test-section {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.lottie-container {
  margin: 15px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  border: 1px dashed #ccc;
  border-radius: 4px;
}

.placeholder {
  text-align: center;
  color: #666;
}

button {
  padding: 10px 20px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background: #1976d2;
}

.error-info {
  margin-top: 20px;
  padding: 15px;
  background: #ffebee;
  border: 1px solid #f44336;
  border-radius: 4px;
}

.error-info pre {
  margin: 10px 0 0 0;
  padding: 10px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}
</style>
