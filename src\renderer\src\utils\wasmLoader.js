/**
 * WebAssembly 加载工具
 * 处理 Electron 环境下的 WASM 加载问题
 */

/**
 * 初始化 WASM 支持
 * @param {string} wasmUrl - WASM 文件的 URL
 * @returns {Promise<boolean>} - 是否成功初始化
 */
export async function initializeWasm(wasmUrl) {
  try {
    console.log('Initializing WASM with URL:', wasmUrl)

    // 检查 WebAssembly 支持
    if (typeof WebAssembly === 'undefined') {
      console.error('WebAssembly is not supported in this environment')
      return false
    }

    // 尝试预加载 WASM 文件
    const response = await fetch(wasmUrl)
    if (!response.ok) {
      throw new Error(`Failed to fetch WASM file: ${response.status}`)
    }

    const wasmBytes = await response.arrayBuffer()
    console.log('WASM file loaded successfully, size:', wasmBytes.byteLength)

    // 验证 WASM 文件
    const isValid = WebAssembly.validate(wasmBytes)
    if (!isValid) {
      throw new Error('Invalid WASM file')
    }

    console.log('WASM file validation successful')
    return true
  } catch (error) {
    console.error('Failed to initialize WASM:', error)
    return false
  }
}

/**
 * 设置 CSP 元标签（如果需要）
 */
export function setupCSPForWasm() {
  // 检查是否已经有 CSP 元标签
  let cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]')

  if (!cspMeta) {
    cspMeta = document.createElement('meta')
    cspMeta.setAttribute('http-equiv', 'Content-Security-Policy')
    document.head.appendChild(cspMeta)
  }

  // 设置允许 WASM 的 CSP 策略
  const cspContent = [
    "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:",
    "worker-src 'self' blob:",
    "child-src 'self' blob:",
    "img-src 'self' data: blob: https:",
    "media-src 'self' data: blob:",
    "font-src 'self' data:",
    "connect-src 'self' https: wss: ws:",
    "style-src 'self' 'unsafe-inline'"
  ].join('; ')

  cspMeta.setAttribute('content', cspContent)
  console.log('CSP meta tag set for WASM support')
}

/**
 * 检查 Electron 环境
 */
export function isElectronEnvironment() {
  return typeof window !== 'undefined' &&
    typeof window.process !== 'undefined' &&
    window.process.type === 'renderer'
}

/**
 * 获取 WASM 加载配置
 */
export function getWasmConfig() {
  const isElectron = isElectronEnvironment()

  return {
    isElectron,
    supportStreaming: !isElectron, // Electron 环境下禁用流式加载
    useArrayBuffer: isElectron,    // Electron 环境下使用 ArrayBuffer
    enableCOOP: !isElectron,       // 非 Electron 环境启用 COOP
    enableCOEP: !isElectron        // 非 Electron 环境启用 COEP
  }
}