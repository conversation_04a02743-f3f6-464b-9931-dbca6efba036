import request from '../utils/request.js'

// 检测是否设置数据集
export function getAiIsSet(params) {
  return window.electron.ipcRenderer.invoke("getAiIsSet", params)
}
// 链接前缀
export function getLinkPrefix(query) {
  return request({
    url: '/api/index/getLinkConfig',
    method: 'post',
    data: query
  })
}
// 添加主账号========================
export function addMainAccount(query) {
  return request({
    url: '/api/orderAccount/addAccount',
    method: 'post',
    data: query
  })
}

// 添加新粉丝
export function addNewFans(query) {
  return request({
    url: '/api/orderFans/addFans',
    method: 'post',
    data: query
  })
}

// 查看粉丝详情
export function getFansInfoSql(query) {
  return request({
    url: '/api/orderFans/getFansInfo',
    method: 'post',
    data: query
  })
}

// 获取粉丝配置
export function getFansConfigSql(query) {
  return request({
    url: '/api/orderFans/getFansConfig',
    method: 'post',
    data: query
  })
}
// 更新粉丝信息
export function updateFansSql(query) {
  return request({
    url: '/api/orderFans/updateFans',
    method: 'post',
    data: query
  })
}
// 根据主账号获取粉丝数据
export function getFansRecordSql(query) {
  return request({
    url: '/api/orderAccount/getFansRecord',
    method: 'post',
    data: query
  })
}
// 根据工单号获取粉丝数据
export function getFansStatiasSql(query) {
  return request({
    url: '/api/workOrder/getFansStatias',
    method: 'post',
    data: query
  })
}

// 创建会话 ======================================
export function addChatInfo(query) {
  return request({
    url: '/api/session/createSession',
    method: 'post',
    data: query
  })
}
// 创建会话---判断数量
export function isNumberOver(query) {
  return request({
    url: '/api/session/isCreateSession',
    method: 'post',
    data: query
  })
}
// 创建会话---删除会话
export function delChatAccount(query) {
  return request({
    url: '/api/session/deleteSession',
    method: 'post',
    data: query
  })
}
// 批量下线账号=============
export function batchOffLine(query) {
  return request({
    url: '/api/orderAccount/batchOffline',
    method: 'post',
    data: query
  })
}
// 会话列表
export function getListSql(query) {
  return request({
    url: '/api/session/getList',
    method: 'post',
    data: query
  })
}
// 通用数据========
// 获取工单
export function getWorkOrderSql(query) {
  return request({
    url: '/api/workOrder/getWorkOrder',
    method: 'post',
    data: query
  })
}
// 获取所有语言
export function getAllLangSql(query) {
  return request({
    url: '/api/translate/getAllLang',
    method: 'post',
    data: query
  })
}
// 获取所有翻译引擎
export function getAllEngineSql(query) {
  return request({
    url: '/api/translate/getAllEngine',
    method: 'post',
    data: query
  })
}

// 获取在线/占用/分配端口数
export function getPortInfoSql(query) {
  return request({
    url: '/api/merchant/portInfo',
    method: 'post',
    data: query
  })
}
// 获取公共快捷回复
export function getQuickReplySql(query) {
  return request({
    url: '/api/quickReply/getQuickReply',
    method: 'post',
    data: query
  })
}
// 获取首页任务信息
export function getTaskSql(query) {
  return request({
    url: '/api/workOrder/getTask',
    method: 'post',
    data: query
  })
}
// // 获取ai助手的网址
export function getAILink(query) {
  return request({
    url: '/api/AiInteract/getAiLink',
    method: 'post',
    data: query
  })
}
// 首页套餐信息
export function getDetailSql(query) {
  return request({
    url: '/api/merchant/getDetail',
    method: 'post',
    data: query
  })
}
// 首页获取站点信息
export function siteInfoSql(query) {
  return request({
    url: '/api/index/siteInfo',
    method: 'post',
    data: query
  })
}
// 获取客户列表
export function getGustListSql(query) {
  return request({
    url: '/api/orderFans/getList',
    method: 'post',
    data: query
  })
}

// 保存聊天记录
export function saveChatContactSql(query) {
  return request({
    url: '/api/chat/addChatRecord',
    method: 'post',
    data: query
  })
}

// 统计数据  /api/index/getBarChart
export function getLineChartSql(query) {
  return request({
    url: '/api/index/getLineChart',
    method: 'post',
    data: query
  })
}

// 统计数据
export function getBarChartSql(query) {
  return request({
    url: '/api/index/getBarChart',
    method: 'post',
    data: query
  })
}
// 推荐话术 -- 快捷回复
export function quickListSql(query) {
  return request({
    url: '/api/QuickReply/getList',
    method: 'post',
    data: query
  })
}

// 翻译图片
export function translateImgSql(query) {
  return request({
    url: '/api/translate/pictureTranslate',
    method: 'post',
    data: query
  })
}
// /api/session/getOpenList  获取左侧账号信息
export function getOpenListSql(query) {
  return request({
    url: '/api/session/getOpenList',
    method: 'post',
    data: query
  })
}
// /api/session/updateStatus  更改回话状态
export function updateStatusSql(query) {
  return request({
    url: '/api/session/updateStatus',
    method: 'post',
    data: query
  })
}

//  保存翻译设置
export function setTranslateConfigrSql(query) {
  return request({
    url: '/api/Translate/setTranslateConfig',
    method: 'post',
    data: query
  })
}

//  查询翻译设置
export function getTranslateConfigSql(query) {
  return request({
    url: '/api/Translate/getTranslateConfig',
    method: 'post',
    data: query
  })
}

//  获取平台列表 --首页
export function getPlatformSql(query) {
  return request({
    url: '/api/index/getPlatform',
    method: 'post',
    data: query
  })
}
//  设置平台开关 --首页
export function setPlatformSql(query) {
  return request({
    url: '/api/index/setPlatform',
    method: 'post',
    data: query
  })
}
