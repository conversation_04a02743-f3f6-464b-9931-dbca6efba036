.custome_list_main_cont {
  display: flex;
  height: 100%;
  .add_btn {
    color: #000;
    line-height: 40px;
    background-color: #f00;
  }
  .soft_set {
    width: 100px;
    background-color: var(--bg-color-cont);
    margin-left: 10px;
    border-radius: 10px;
    // transition: all 0.3s;
  }
  .open_soft_set {
    width: 450px !important;
  }
  .custome_list_cont {
    width: calc(100% - 110px);
    border-radius: 10px;
    overflow-y: auto;
    background-color: var(--bg-color-cont);
    // transition: all 0.3s;
    padding: 20px;
    .chatTit {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: var(--font-color-login);
      .tit {
        h2 {
          font-family: Nunito Sans;
          font-size: 24px;
          font-weight: 700;
          line-height: 32px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          padding-bottom: 3px;
        }
        p {
          display: flex;
          align-items: center;
          span {
            font-family: Nunito Sans;
            font-size: 16px;
            font-weight: 400;
            line-height: 21.33px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #2072f7;
            margin-right: 10px;
          }
        }
      }
      .btns {
        display: flex;
        .item {
          display: flex;
          background-color: var(--bg-color-index-echars);
          margin: 0 10px;
          height: 35px;
          align-items: center;
          border-radius: 30px;
          line-height: 35px;
          text-align: center;
          padding: 0 20px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          img {
            width: 20px;
            height: 20px;
            margin-right: 15px;
          }
          p {
            width: Hug (109px) px;
            height: Hug (25px) px;
            top: 10px;
            left: 42px;
            gap: 14px;
            opacity: 0px;
          }
        }
      }
      .clock {
        display: flex;
        align-items: center;
        span {
          padding-right: 8px;
          color: #8f95b2;
          font-family: Nunito Sans;
          font-size: 18.67px;
          font-weight: 400;
          line-height: 25.46px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
        }
      }
    }
    .chatList {
      padding-top: 30px;
      .el-table__header-wrapper {
        background-color: var(--bg-color-cont);
      }
      tr {
        background-color: var(--bg-color-cont);
        th.el-table__cell {
          background-color: var(--bg-color-index-echars);
          border-bottom: 0;
        }
        th.el-table__cell:nth-child(1) {
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
          border-left: 1px solid var(--bg-color-cont);
        }
        th.el-table__cell:last-child {
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
          border-right: 1px solid var(--bg-color-cont);
        }
        td.el-table__cell {
          border-bottom: 1px solid var(--bg-color-index-echars);
        }
        .el-table .cell {
          overflow: auto;
          border-bottom: 0;
        }
      }
      .el-table {
        border-bottom: 1px solid var(--bg-color-index-echars) !important;
        --el-table-border-color: '';
        --el-table-bg-color: '';
      }
      .setBtn {
        background-color: #dde4ff;
        border-radius: 40px;
        color: #2072f7;
        box-shadow: 0px 8px 10px #8f95b226;
      }
      .setBtn.del {
        background-color: #fed4e7;
        color: #fd71af;
      }
      .sale_info {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        p {
          color: #2072f7;
          border: 1px solid #2072f7;
          border-radius: 8px;
          font-family: Inter;
          font-size: 14px;
          font-weight: 400;
          padding: 0 10px;
          margin: 0 5px;
        }
      }
      .tags {
        display: flex;
        flex-wrap: wrap;
        p {
          color: #00b884;
          border: 1px solid #00b884;
          border-radius: 8px;
          font-family: Inter;
          font-size: 14px;
          font-weight: 400;
          padding: 0 10px;
          margin: 0 5px;
        }
      }
      .yun {
        p {
          width: 24px;
          height: 24px;
          border: 1px solid #f3f3f3;
          border-radius: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        p.on {
          background-color: #2072f7;
        }
      }
    }
    .pageInfo {
      padding-top: 30px;
      .el-pagination {
        justify-content: center;
      }
    }
    .custome_empty {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      p {
        color: #8f95b2;
        font-family: Inter;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
      }
    }

    .search_info .form {
      padding-top: 20px;
      display: flex;
      .el-select__wrapper,
      .el-input__wrapper {
        width: 175px;
        height: 35px;
        border-radius: 8px;
        background-color: var(--bg-color-index-echars);
        box-shadow: 0 0 0 transparent;
      }
      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;
      }
      .search_btn {
        display: flex;
        background-color: #2072f7;
        color: #fff;
        align-items: center;
        border-radius: 10px;
        padding: 0 12px;
        span {
          padding-left: 8px;
        }
      }
    }
  }
  .open_custome_list_cont {
    width: calc(100% - 460px) !important;
  }
  .shadow {
    display: flex;
    align-items: center;
    justify-content: center;
    .userinfo_flag {
      z-index: 999;
      background-color: #fff;
      width: 80%;
      padding: 20px;
      max-width: 1350px;
      min-width: 960px;
      margin-bottom: 20px;
      border-radius: 20px;
      .flag_tit {
        display: flex;
        justify-content: space-between;
        h2 {
          font-family: Inter;
          font-size: 24px;
          font-weight: 600;
          letter-spacing: 0.035em;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #232030;
        }
      }
      .flag_tag {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #2072f7;
        height: 48px;
        width: 240px;
        margin: 0 auto;
        line-height: 48px;
        border-radius: 10px;
        .item {
          width: 120px;
          text-align: center;
          font-family: Inter;
          font-size: 16px;
          font-weight: 400;
          letter-spacing: 0.035em;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #2072f7;
        }
        .item:first-child.on {
          background-color: #2072f7;
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
          color: #fff;
        }
        .item:last-child.on {
          background-color: #2072f7;
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
          color: #fff;
        }
      }
      .flag_form {
        h3 {
          text-align: center;
          position: relative;
          line-height: 35px;
          span {
            color: #232030;
            font-family: Inter;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.035em;
            background-color: #fff;
            position: relative;
            z-index: 99;
            padding: 0 50px;
          }
        }
        h3:after {
          content: '';
          width: 100%;
          position: absolute;
          top: 18px;
          left: 0;
          border-bottom: 1px solid #dddfe7;
        }
        .flag_item {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          padding: 30px 0;
          .el-form-item {
            width: 23%;
            margin-right: 2%;
          }
          .sex_info {
            display: flex;
            align-items: center;
            .item {
              width: 70px;
              display: flex;
              align-items: center;
              align-items: center;
              p {
                width: 24px;
                height: 24px;
                border: 1px solid #8f95b2;
                border-radius: 6px;
                margin-right: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
              span {
                font-size: 16px;
                color: #8f95b2;
              }
            }
            .item.on {
              p {
                background-color: #2072f7;
                border: 1px solid #2072f7;
              }
              span {
                color: #2072f7;
              }
            }
          }
        }
        .flag_item.sale {
          .el-form-item {
            width: 31%;
            margin-right: 2%;
            margin-bottom: 35px;
          }
          .el-form-item:nth-child(5) {
            width: 64%;
          }
        }
        .flag_item.conpany {
          .el-form-item {
            width: 48%;
          }
          .el-form-item:nth-child(5) {
            width: 64%;
          }
        }
        .btns {
          display: flex;
          align-items: center;
          justify-content: center;
          .item {
            background-color: #2072f7;
            color: #fff;
            width: 120px;
            line-height: 44px;
            height: 44px;
            text-align: center;
            border-radius: 10px;
            margin: 0 10px;
          }
          .item:first-child {
            background-color: #dddfe7;
            color: #8f95b2;
          }
        }
        .flag_remark {
          padding: 30px 0 0;
          .remarks {
            border: 1px solid #dddfe7;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            min-height: 125px;
          }
          .input_remark {
            .el-textarea__inner {
              height: 125px;
              border-radius: 10px;
              padding: 10px;
              background-color: #dddfe7;
            }
            padding-bottom: 30px;
          }
        }
      }
    }
  }
}
