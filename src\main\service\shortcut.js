import { globalShortcut, app } from "electron";
import { GlobalObject } from "../global";




export async function initShortcut() {
  try {
    await app.whenReady()

    // const success = globalShortcut.register('Escape', () => {
    //   console.log('你按了 ESC 键（主进程监听）')
    //   GlobalObject.mainWindow.hide()
    // })

    // if (!success) {
    //   console.log('快捷键注册失败')
    // }

    app.on("quit", () => {
      globalShortcut.unregisterAll()
    })
  } catch (error) {

  }
}