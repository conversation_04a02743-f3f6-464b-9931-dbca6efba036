.sys_base{
	.sys_left_b{
		width: 85px;
	}
	.small_tab{
		margin-left:102px!important;
		 width: calc(100% - 0px)!important;
		 .quick_cont{
			 width: calc(100% - 0px)!important;
		 }
	}
	
}
.quick_main_cont{
	display: flex;
	height: 100%;
	width: calc(100% - 122px)!important;
	// background-color: var(--bg-color-cont);
	.add_btn{
		color:#000;
		line-height: 40px;
		background-color: #f00;
	}
	.quick_cont{
		width: calc(100% - 110px);
		border-radius: 10px;
		height: 100%;
		overflow-y: auto;;
		background-color: transparent;
		transition: all 0.3s;
		padding:0px;
		display: flex;
		.tit{
			h2{
				font-family: Nunito Sans;
				font-size: 24px;
				font-weight: 700;
				line-height: 32px;
				text-align: left;
				text-underline-position: from-font;
				text-decoration-skip-ink: none;
				color: var(--font-color-login);
			}
			span{
				font-family: Nunito Sans;
				font-size: 16px;
				font-weight: 400;
				line-height: 21.33px;
				text-align: left;
				text-underline-position: from-font;
				text-decoration-skip-ink: none;
				color:#DDDFE7;
			}
		}
		.cont_left{
			width: 316px;
			height: 100%;
			padding:20px;
			overflow-y: auto;
			background-color: var(--bg-color-cont);
			border-radius: 10px;
			.search{
				padding:20px 0;
				.menu_btn{
					display: flex;
					align-items: center;
					justify-content: space-between;
					p{
						color:var(--font-color-login);
						font-size: 20px;
					}
					div{
						display: flex;
						align-items: center;
						color:#5577FF;
						font-size: 16px;
						span{
							padding-left:6px;
						}
					}
				}
				.input{
					padding:10px 0;
					input{
						width: 100%;
						outline: 0;
						border:1px solid var(--bg-color-index-echars);
						border-radius: 5px;
						line-height: 45px;
						height: 45px;
						padding:0 10px;
						background-color: var(--bg-color-index-echars);
					}
				}
			}
			.teamList{
				color:#626DA1;
				.items{
					.name{
						font-size: 18px;
						margin-bottom: 10px;
						line-height: 36px;
						height: 36px;
						position: relative;
						.el-input__wrapper{
								width: 100%;
								height: 36px;
								outline: 0;
								border:1px solid #eee;
								border-radius: 10px;
								padding:0 15px;
							}
						}
						.name_son{
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;
							span{
								display: block;
								width: calc(100% - 30px);
							}
							.addSon{
								display: none;
								margin-right: 10px;
								img{
									width: 20px;
									height: 20px;
								}
							}
							
						}
						.name_son:hover{
							.addSon{
								display: flex;
								align-items: center;
							}
						}
						
					}
					.item{
						.name.sel_names{
							background-color: #5577FF!important;
							color:#fff;
						}
						.name{
							background-color: var(--bg-color-index-echars);
							padding-left:50px;
							border-radius: 20px;
					}
				}
				.items > .name{
					display: flex;
					align-items: center;
				}
				.items > .name:before{
					content: '';
					width: 0;
					height: 0;
					margin-right: 8px;
					display: block;
					border-left: 5px solid transparent;
					border-right: 5px solid transparent;
					transition: all 0.3s;
					transform: rotate(180deg);
					border-top: 8px solid #626DA1; /* Change the color as needed */
				}
				.items > .name.name_click:before{
					content: '';
					width: 0;
					height: 0;
					margin-right: 8px;
					display: block;
					border-left: 5px solid transparent;
					border-right: 5px solid transparent;
					border-top: 8px solid #626DA1; /* Change the color as needed */
					transform: rotate(0deg);
				}
			}
		}
		.cont_right{
			width: calc(100% - 316px);
			background-color: var(--bg-color-cont);
			margin-left: 10px;
			border-radius: 10px;
			padding:20px;
			.right_header{
				display: flex;
				align-items: center;
				justify-content: flex-end;
				color:var(--font-color-login);
				.item{
					display: flex;
					align-items: center;
					background-color: var(--bg-color-index-echars);
					margin-left: 20px;
					padding:0 20px;
					line-height: 40px;
					font-size: 18px;
					border-radius: 20px;
					img{
						width: 20px;
						height: 20px;
					}
					p{
						padding-left:8px;
					}
					span{
						font-size: 32px;
						margin-top: -8px;;
					}
				}
			}
			.quick_message{
				padding-top:30px;
				.item{
					display: flex;
					align-items: center;
					margin-bottom: 10px;
					background-color: var(--bg-color-index-echars);
					.del{
						display: flex;
						align-items: center;
						width: 0px;
						overflow: hidden;
						transition: all 0.3s;
						img{
							width: 24px;
							height: 24px;
						}
					}
					.input{
						width: 100%;
						margin-right:10px;
						transition: all 0.3s;
						.el-input__wrapper{
							background-color: var(--bg-color-index-echars);
						}
						.el-input__inner{
							height: 40px;
							background-color: var(--bg-color-index-echars);
						}
					}
				}
				.item:hover{
					.del{
						width: 24px;
					}
					.input{
						width: calc(100% - 34px);
					}
				}
			}
		}
	}
}